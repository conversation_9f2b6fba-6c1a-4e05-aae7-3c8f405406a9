#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Inference Engine for Fishial AI
推理引擎 - 负责模型推理和预测
"""

import os
import sys
import json
import time
import logging
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import hashlib

# 图像处理
try:
    from PIL import Image
    import numpy as np
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    logging.warning("PIL not available, using basic image processing")

# PyTorch相关
try:
    import torch
    import torchvision.transforms as transforms
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    logging.warning("PyTorch not available, using mock inference")

# 模型相关依赖
try:
    import timm
    import faiss
    from scipy.stats import entropy
    from sklearn.metrics import pairwise_distances
    ADVANCED_LIBS_AVAILABLE = True
except ImportError:
    ADVANCED_LIBS_AVAILABLE = False
    logging.warning("Advanced libraries (timm, faiss, scipy, sklearn) not available")

from model_manager import get_model_manager

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealFishClassifier:
    """真实的鱼类分类器"""

    def __init__(self, model_path: str):
        """初始化分类器"""
        self.model_path = model_path
        self.model = None
        self.device = "cpu"  # 默认使用CPU
        self.labels_mapping = {}

        # 尝试加载模型
        self._load_model()

    def _load_model(self):
        """加载模型"""
        try:
            # 添加模型路径到Python路径
            model_dir = self.model_path
            if model_dir not in sys.path:
                sys.path.insert(0, model_dir)

            # 导入模型的推理模块
            import inference
            from inference import EmbeddingClassifier

            # 配置
            config = {
                "model": {
                    "path": os.path.join(model_dir, "model.ckpt"),
                    "device": self.device
                },
                "dataset": {
                    "path": os.path.join(model_dir, "database.pt")
                },
                "log_level": "WARNING"
            }

            # 初始化分类器
            self.model = EmbeddingClassifier(config)

            # 加载标签映射
            labels_path = os.path.join(model_dir, "labels.json")
            if os.path.exists(labels_path):
                with open(labels_path, 'r', encoding='utf-8') as f:
                    self.labels_mapping = json.load(f)
            else:
                self.labels_mapping = {}

            logging.info(f"真实模型加载成功: {model_dir}")
            return True

        except Exception as e:
            logging.error(f"模型加载失败: {e}")
            self.model = None
            return False

    def predict(self, image_path: str) -> Dict[str, Any]:
        """预测图像中的鱼类"""
        if self.model is None:
            raise Exception("模型未加载")

        try:
            # 加载图像
            image = Image.open(image_path).convert('RGB')
            image_array = np.array(image)

            # 模型推理
            results = self.model(image_array)

            # 检查结果格式
            if isinstance(results, list) and len(results) > 0:
                if isinstance(results[0], list) and len(results[0]) > 0:
                    # 获取最佳预测结果
                    best_result = results[0][0]  # 第一个图像的第一个结果

                    species_name = best_result.name
                    species_id = best_result.species_id
                    confidence = best_result.accuracy

                    # 查找中文名称
                    chinese_name = species_name
                    if self.labels_mapping and str(species_id) in self.labels_mapping:
                        chinese_name = self.labels_mapping[str(species_id)]

                    return {
                        "species_id": species_id,
                        "species_name": chinese_name,
                        "english_name": species_name,
                        "scientific_name": species_name,
                        "confidence": float(confidence),
                        "distance": float(best_result.distance),
                        "success": True
                    }
            else:
                # 如果标准推理失败，尝试使用更低的阈值
                logging.warning("标准推理返回空结果，尝试使用低阈值...")

                try:
                    # 直接进行推理，使用更低的阈值
                    tensor = self.model.transform(image).unsqueeze(0).to(self.model.device)

                    with torch.no_grad():
                        embeddings, archead_logits, _ = self.model.model(tensor)

                    # 使用更低的阈值
                    low_threshold_results = self.model.get_top_neighbors_from_embeddings(
                        embeddings,
                        topk_centroid=10,
                        topk_neighbors=20,
                        centroid_threshold=0.1,  # 非常低的阈值
                        neighbor_threshold=0.1   # 非常低的阈值
                    )

                    if low_threshold_results and len(low_threshold_results) > 0:
                        first_result = low_threshold_results[0]
                        if len(first_result) > 0:
                            # 找到相似度最高的结果
                            best_label = max(first_result.keys(), key=lambda k: first_result[k]['similarity'])
                            best_data = first_result[best_label]

                            # 获取物种ID
                            species_id = self.model.label_to_species_id.get(best_label, 0)
                            confidence = best_data['similarity']

                            # 查找中文名称
                            chinese_name = best_label
                            if self.labels_mapping and str(species_id) in self.labels_mapping:
                                chinese_name = self.labels_mapping[str(species_id)]

                            return {
                                "species_id": species_id,
                                "species_name": chinese_name,
                                "english_name": best_label,
                                "scientific_name": best_label,
                                "confidence": float(confidence),
                                "distance": 1.0 - float(confidence),
                                "success": True,
                                "low_threshold": True
                            }

                    return {
                        "success": False,
                        "error": "即使使用低阈值也未找到匹配的鱼类"
                    }

                except Exception as e2:
                    logging.error(f"低阈值推理也失败: {e2}")
                    return {
                        "success": False,
                        "error": f"推理失败: {str(e2)}"
                    }

        except Exception as e:
            logging.error(f"预测失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }


class InferenceEngine:
    """推理引擎类"""

    def __init__(self):
        """初始化推理引擎"""
        self.model_manager = get_model_manager()
        self.models = {}
        self.transforms = {}
        self.device = self._get_device()
        self.real_classifier = None

        # 初始化图像变换
        self._init_transforms()

        # 尝试加载真实模型
        self._load_real_models()

        # 预加载模型（如果配置启用）
        if self.model_manager.get_config('performance').get('model_cache', True):
            self._preload_models()

    def _load_real_models(self):
        """加载真实模型"""
        try:
            # 获取分类模型路径
            classification_model_path = self.model_manager.get_model_path('classification')
            if classification_model_path:
                model_dir = os.path.dirname(classification_model_path)
                self.real_classifier = RealFishClassifier(model_dir)
                if self.real_classifier.model is not None:
                    logging.info("真实分类模型加载成功")
                else:
                    logging.warning("真实分类模型加载失败，将使用模拟模式")
                    self.real_classifier = None
            else:
                logging.warning("未找到分类模型文件")

        except Exception as e:
            logging.error(f"加载真实模型时出错: {e}")
            self.real_classifier = None
    
    def _get_device(self) -> str:
        """获取计算设备"""
        if not TORCH_AVAILABLE:
            return "cpu"
            
        performance_config = self.model_manager.get_config('performance')
        use_gpu = performance_config.get('use_gpu', False)
        
        if use_gpu and torch.cuda.is_available():
            device_id = performance_config.get('gpu_device', 0)
            device = f"cuda:{device_id}"
            logger.info(f"使用GPU设备: {device}")
        else:
            device = "cpu"
            logger.info("使用CPU设备")
            
        return device
    
    def _init_transforms(self):
        """初始化图像变换"""
        if not PIL_AVAILABLE or not TORCH_AVAILABLE:
            return
            
        # 获取数据配置
        data_config = self.model_manager.get_config('data')
        preprocessing = data_config.get('preprocessing', {})
        
        mean = preprocessing.get('mean', [0.485, 0.456, 0.406])
        std = preprocessing.get('std', [0.229, 0.224, 0.225])
        
        # 分类模型变换
        classification_config = self.model_manager.get_config('models').get('classification', {})
        input_size = classification_config.get('input_size', [224, 224])
        
        self.transforms['classification'] = transforms.Compose([
            transforms.Resize(input_size),
            transforms.CenterCrop(input_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=mean, std=std)
        ])
        
        # 检测模型变换
        detection_config = self.model_manager.get_config('models').get('detection', {})
        detection_size = detection_config.get('input_size', [640, 640])
        
        self.transforms['detection'] = transforms.Compose([
            transforms.Resize(detection_size),
            transforms.ToTensor()
        ])
        
        # 分割模型变换
        segmentation_config = self.model_manager.get_config('models').get('segmentation', {})
        segmentation_size = segmentation_config.get('input_size', [416, 416])
        
        self.transforms['segmentation'] = transforms.Compose([
            transforms.Resize(segmentation_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=mean, std=std)
        ])
        
        logger.info("图像变换初始化完成")
    
    def _preload_models(self):
        """预加载模型"""
        logger.info("开始预加载模型...")
        
        # 这里暂时不实际加载模型，因为需要具体的模型架构
        # 在实际部署时，需要根据具体的模型文件格式来加载
        
        available_models = self.model_manager.list_available_models()
        for model_type, available in available_models.items():
            if available:
                logger.info(f"模型可用: {model_type}")
            else:
                logger.warning(f"模型不可用: {model_type}")
    
    def _load_image(self, image_path: str) -> Optional[Image.Image]:
        """
        加载图像
        
        Args:
            image_path: 图像路径
            
        Returns:
            PIL图像对象
        """
        if not PIL_AVAILABLE:
            logger.error("PIL不可用，无法加载图像")
            return None
            
        try:
            image = Image.open(image_path)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            return image
        except Exception as e:
            logger.error(f"图像加载失败: {image_path} - {e}")
            return None
    
    def _preprocess_image(self, image: Image.Image, model_type: str) -> Optional[Any]:
        """
        预处理图像
        
        Args:
            image: PIL图像对象
            model_type: 模型类型
            
        Returns:
            预处理后的张量
        """
        if not TORCH_AVAILABLE or model_type not in self.transforms:
            return None
            
        try:
            transform = self.transforms[model_type]
            tensor = transform(image)
            # 添加批次维度
            tensor = tensor.unsqueeze(0)
            return tensor.to(self.device)
        except Exception as e:
            logger.error(f"图像预处理失败: {e}")
            return None
    
    def classify_fish(self, image_path: str) -> Dict[str, Any]:
        """
        鱼类分类

        Args:
            image_path: 图像路径

        Returns:
            分类结果字典
        """
        start_time = time.time()

        # 尝试使用真实模型
        if self.real_classifier is not None and TORCH_AVAILABLE and ADVANCED_LIBS_AVAILABLE:
            try:
                logger.info("使用真实模型进行推理")
                result = self.real_classifier.predict(image_path)

                if result.get("success", False):
                    processing_time = time.time() - start_time
                    return {
                        "success": True,
                        "species_id": result["species_id"],
                        "species_name": result["species_name"],
                        "english_name": result["english_name"],
                        "scientific_name": result["scientific_name"],
                        "confidence": result["confidence"],
                        "processing_time": processing_time,
                        "model_used": True,
                        "mock_result": False,
                        "distance": result.get("distance", 0.0)
                    }
                else:
                    logger.warning(f"真实模型推理失败: {result.get('error', 'Unknown error')}")

            except Exception as e:
                logger.error(f"真实模型推理异常: {e}")

        # 后备方案：使用模拟结果
        logger.info("使用模拟模式进行推理")
        return self._mock_classification_result(image_path, start_time)
    
    def detect_fish(self, image_path: str) -> Dict[str, Any]:
        """
        鱼类检测
        
        Args:
            image_path: 图像路径
            
        Returns:
            检测结果字典
        """
        start_time = time.time()
        
        # 检查模型可用性
        if not self.model_manager.list_available_models().get('detection', False):
            return self._mock_detection_result(image_path, start_time)
        
        # 如果PyTorch不可用，使用模拟结果
        if not TORCH_AVAILABLE or not PIL_AVAILABLE:
            return self._mock_detection_result(image_path, start_time)
        
        try:
            # 实际检测逻辑（待实现）
            return self._mock_detection_result(image_path, start_time)
            
        except Exception as e:
            logger.error(f"检测推理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "processing_time": time.time() - start_time
            }
    
    def segment_fish(self, image_path: str) -> Dict[str, Any]:
        """
        鱼类分割
        
        Args:
            image_path: 图像路径
            
        Returns:
            分割结果字典
        """
        start_time = time.time()
        
        # 检查模型可用性
        if not self.model_manager.list_available_models().get('segmentation', False):
            return self._mock_segmentation_result(image_path, start_time)
        
        # 如果PyTorch不可用，使用模拟结果
        if not TORCH_AVAILABLE or not PIL_AVAILABLE:
            return self._mock_segmentation_result(image_path, start_time)
        
        try:
            # 实际分割逻辑（待实现）
            return self._mock_segmentation_result(image_path, start_time)
            
        except Exception as e:
            logger.error(f"分割推理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "processing_time": time.time() - start_time
            }
    
    def _mock_classification_result(self, image_path: str, start_time: float) -> Dict[str, Any]:
        """生成模拟分类结果"""
        import random
        
        labels = self.model_manager.get_labels()
        if not labels:
            # 使用默认标签
            labels = {
                "0": "鲫鱼 (Crucian Carp)",
                "1": "草鱼 (Grass Carp)", 
                "2": "鲤鱼 (Common Carp)",
                "3": "黑鱼 (Northern Snakehead)",
                "4": "鲈鱼 (Bass)"
            }
        
        # 随机选择一个鱼种
        fish_ids = list(labels.keys())
        predicted_id = random.choice(fish_ids)
        species_name = labels[predicted_id]
        
        # 生成置信度
        confidence = round(random.uniform(0.75, 0.98), 3)
        
        # 解析中英文名称
        if "(" in species_name and ")" in species_name:
            parts = species_name.split("(")
            english_name = parts[0].strip()
            chinese_part = parts[1].replace(")", "").strip()
            chinese_name = chinese_part
        else:
            english_name = species_name
            chinese_name = "未知鱼种"
        
        processing_time = time.time() - start_time
        
        return {
            "success": True,
            "model_type": "classification",
            "species_id": int(predicted_id),
            "species_name": chinese_name,
            "english_name": english_name,
            "scientific_name": english_name,
            "confidence": confidence,
            "processing_time": round(processing_time, 3),
            "image_path": image_path,
            "mock_result": True
        }
    
    def _mock_detection_result(self, image_path: str, start_time: float) -> Dict[str, Any]:
        """生成模拟检测结果"""
        import random
        
        processing_time = time.time() - start_time
        
        # 模拟检测框
        detections = []
        num_detections = random.randint(1, 3)
        
        for i in range(num_detections):
            detection = {
                "bbox": [
                    random.randint(50, 200),   # x
                    random.randint(50, 200),   # y  
                    random.randint(100, 300),  # width
                    random.randint(100, 300)   # height
                ],
                "confidence": round(random.uniform(0.6, 0.95), 3),
                "class_id": 0,
                "class_name": "fish"
            }
            detections.append(detection)
        
        return {
            "success": True,
            "model_type": "detection", 
            "detections": detections,
            "num_detections": len(detections),
            "processing_time": round(processing_time, 3),
            "image_path": image_path,
            "mock_result": True
        }
    
    def _mock_segmentation_result(self, image_path: str, start_time: float) -> Dict[str, Any]:
        """生成模拟分割结果"""
        processing_time = time.time() - start_time
        
        return {
            "success": True,
            "model_type": "segmentation",
            "mask_available": True,
            "mask_path": None,  # 实际实现时应该生成掩码文件
            "processing_time": round(processing_time, 3),
            "image_path": image_path,
            "mock_result": True
        }


# 全局推理引擎实例
inference_engine = None

def get_inference_engine() -> InferenceEngine:
    """获取全局推理引擎实例"""
    global inference_engine
    if inference_engine is None:
        inference_engine = InferenceEngine()
    return inference_engine


if __name__ == "__main__":
    # 测试推理引擎
    engine = InferenceEngine()
    
    print("=== 推理引擎测试 ===")
    print(f"设备: {engine.device}")
    print(f"PyTorch可用: {TORCH_AVAILABLE}")
    print(f"PIL可用: {PIL_AVAILABLE}")
    
    # 测试分类（使用测试图像）
    test_image = "uploads/test_image.jpg"
    if os.path.exists(test_image):
        result = engine.classify_fish(test_image)
        print(f"分类结果: {result}")
    else:
        print("测试图像不存在")
