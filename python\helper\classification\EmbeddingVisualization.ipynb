{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9b0e2897", "metadata": {}, "outputs": [], "source": ["import sys\n", "#Change path specificly to your directories\n", "sys.path.insert(1, '/home/<USER>/Fishial/Object-Detection-Model')\n", "\n", "import time\n", "import torch\n", "import os\n", "import cv2\n", "import math \n", "import matplotlib.pyplot as plt\n", "import torchvision.models as models\n", "import numpy as np\n", "from torch import nn\n", "from torch.optim import Optimizer, SGD\n", "from torch.utils.data import DataLoader\n", "from torch.utils.data.dataset import Dataset\n", "from torchvision import transforms\n", "from sklearn.neighbors import KDTree\n", "import pandas as pd\n", "from module.classification_package.src.utils import read_json, save_json, classify_by_database\n", "from module.classification_package.interpreter_classifier import ClassifierFC\n", "from module.classification_package.interpreter_embeding import EmbeddingClassifier\n", "# from module.classification_package.interpreter_embeding_data import EmbeddingClassifierData\n", "from module.segmentation_package.interpreter_segm import SegmentationInference\n", "from module.classification_package.src.dataset import FishialDataset\n", "# from module.segmentation_package.src.utils import resize_image\n", "from PIL import Image\n", "import numpy as np\n", "import random\n", "import sklearn.metrics.pairwise\n", "import scipy.spatial.distance\n", "import copy\n", "import json\n", "import time\n", "import requests\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# import FiftyOne\n", "import fiftyone as fo\n", "import fiftyone.zoo as foz\n", "\n", "from module.segmentation_package.interpreter_segm import SegmentationInference\n", "import fiftyone.brain as fob\n", "\n", "\n", "from sklearn.metrics import classification_report\n", "from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay\n", "from os import listdir\n", "from module.classification_package.src.utils import read_json, save_json\n", "from os.path import isfile, join"]}, {"cell_type": "code", "execution_count": 2, "id": "f5386d4c", "metadata": {}, "outputs": [], "source": ["def get_class_and_id(idx_data, ann_id):\n", "    for i in idx_data:\n", "        for k_id, k in enumerate(idx_data[i]['annotation_id']):\n", "            if k == ann_id:\n", "                return i,k_id"]}, {"cell_type": "code", "execution_count": 16, "id": "a6e60d5e", "metadata": {}, "outputs": [], "source": ["dataset_full = fo.load_dataset(\"fish-classification-184\")\n", "selected_data = dataset_full.match_tags(['val', 'train'])"]}, {"cell_type": "code", "execution_count": 4, "id": "8a58d522", "metadata": {}, "outputs": [], "source": ["main_path = r'/home/<USER>/Fishial/output/classification/resnet_18_184_train_06_12'\n", "embedding_tensor = torch.load(os.path.join(main_path, 'embeddings.pt'))\n", "idx_data = read_json(os.path.join(main_path, 'idx.json'))\n", "labels = read_json(os.path.join(main_path, 'labels.json'))\n", "# labels_reverse = {labels[label]: label for label in labels}"]}, {"cell_type": "code", "execution_count": 28, "id": "d9d1eef5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Left : 30308/30309\r"]}], "source": ["embeddings_list = []\n", "for idx, sample in enumerate(selected_data):\n", "    print(f\"Left : {idx}/{len(selected_data)}\", end='\\r')\n", "    ann_id = sample['annotation_id']\n", "    class_id, val_id = get_class_and_id(idx_data, ann_id)\n", "    embedding = embedding_tensor[int(class_id)][int(val_id)]\n", "    embeddings_list.append(embedding)\n", "    output = classify_by_database(embedding_tensor, embedding)\n", "    if sample['polyline']['label'] != labels[str(class_id)]:\n", "        sample['tags'].append(\"misstake\")\n", "#     sample['predictions_distance'] = output[0][1].item()\n", "    sample.save()"]}, {"cell_type": "code", "execution_count": 18, "id": "ad30133f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating visualization...\n", "UMAP(random_state=51, verbose=True)\n", "Mon Dec 12 10:41:35 2022 Construct fuzzy simplicial set\n", "Mon Dec 12 10:41:35 2022 Finding Nearest Neighbors\n", "Mon Dec 12 10:41:35 2022 Building RP forest with 14 trees\n", "Mon Dec 12 10:41:35 2022 NN descent for 15 iterations\n", "\t 1  /  15\n", "\t 2  /  15\n", "\t 3  /  15\n", "\tStopping threshold met -- exiting after 3 iterations\n", "Mon Dec 12 10:41:36 2022 Finished Nearest Neighbor Search\n", "Mon Dec 12 10:41:36 2022 Construct embedding\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5830beab577f4d6c9e1d14108e7c0d80", "version_major": 2, "version_minor": 0}, "text/plain": ["Epochs completed:   0%|            0/200 [00:00]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Mon Dec 12 10:41:45 2022 Finished embedding\n"]}], "source": ["results = fob.compute_visualization(\n", "    selected_data,\n", "    embeddings=embeddings_list,\n", "    num_dims=2,\n", "    method=\"umap\",\n", "    brain_key=\"mnist_test\",\n", "    verbose=True,\n", "    seed=51,)"]}, {"cell_type": "code", "execution_count": 19, "id": "1f709333", "metadata": {}, "outputs": [{"data": {"text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6790a0255f3442e5866f2eefb58360ba", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'customdata': array(['6380bc22ccb46092e99addc0', '6380bc22ccb46092e99addc2',\n", "    …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot embeddings colored by ground truth label\n", "plot = results.visualize(labels=\"polyline.label\")\n", "plot.show(height=720)\n", "\n", "# Attach plot to session\n", "session.plots.attach(plot)"]}, {"cell_type": "code", "execution_count": 29, "id": "ee598a40", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"800\"\n", "            src=\"http://localhost:5151/?notebook=true&handleId=4492d94a-d021-4c9f-83f1-992e337a695b\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x7f4beaca52b0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["session = fo.launch_app(view=selected_data)"]}, {"cell_type": "code", "execution_count": 22, "id": "39996e16", "metadata": {}, "outputs": [], "source": ["dataset_full.delete_sample_field(\"predictions\")"]}, {"cell_type": "code", "execution_count": 23, "id": "b7466a2d", "metadata": {}, "outputs": [], "source": ["dataset_full.save()"]}, {"cell_type": "code", "execution_count": 30, "id": "51ab961d", "metadata": {}, "outputs": [{"data": {"text/plain": ["Dataset:     fish-classification-184\n", "Media type:  image\n", "Num samples: 30309\n", "Tags:        ['odm_false', 'odm_true', 'rest', 'train', 'val']\n", "Sample fields:\n", "    id:            fiftyone.core.fields.ObjectIdField\n", "    filepath:      fiftyone.core.fields.StringField\n", "    tags:          fiftyone.core.fields.ListField(fiftyone.core.fields.StringField)\n", "    metadata:      fiftyone.core.fields.EmbeddedDocumentField(fiftyone.core.metadata.Metadata)\n", "    polyline:      fiftyone.core.fields.EmbeddedDocumentField(fiftyone.core.labels.Polyline)\n", "    annotation_id: fiftyone.core.fields.StringField\n", "    image_id:      fiftyone.core.fields.StringField\n", "    width:         fiftyone.core.fields.IntField\n", "    height:        fiftyone.core.fields.IntField\n", "    area_conf:     fiftyone.core.fields.FloatField\n", "    drawn_fish_id: fiftyone.core.fields.IntField\n", "View stages:\n", "    1. MatchTags(tags=['val', 'train'], bool=True)"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["selected_data"]}, {"cell_type": "code", "execution_count": null, "id": "c1f44270", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "detectron2_env", "language": "python", "name": "detectron2_env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.13"}}, "nbformat": 4, "nbformat_minor": 5}