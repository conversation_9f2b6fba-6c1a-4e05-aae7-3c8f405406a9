{"cells": [{"cell_type": "markdown", "id": "52b07c2f", "metadata": {}, "source": ["# IMPORT"]}, {"cell_type": "code", "execution_count": null, "id": "2fe8c8c2", "metadata": {}, "outputs": [], "source": ["import os\n", "import torch\n", "import time\n", "from detectron2.export import (\n", "    STABLE_ONNX_OPSET_VERSION,\n", "    TracingAdapter,\n", "    dump_torchscript_IR,\n", "    scripting_with_instances,\n", ")\n", "from detectron2.modeling import GeneralizedRCNN, RetinaNet, build_model\n", "from detectron2.data import build_detection_test_loader, detection_utils\n", "from detectron2.config import get_cfg\n", "from detectron2.projects.point_rend import ColorAugSSDTransform, add_pointrend_config\n", "\n", "import detectron2.data.transforms as T\n", "from detectron2.utils.file_io import PathManager"]}, {"cell_type": "code", "execution_count": null, "id": "264c668f", "metadata": {}, "outputs": [], "source": ["# return inputs for segmentation model by using image path\n", "\n", "def get_sample_inputs(sample_image):\n", "    \n", "    original_image = detection_utils.read_image(sample_image, format=cfg.INPUT.FORMAT)\n", "    # Do same preprocessing as DefaultPredictor\n", "    aug = T.ResizeShortestEdge(\n", "        [cfg.INPUT.MIN_SIZE_TEST, cfg.INPUT.MIN_SIZE_TEST], cfg.INPUT.MAX_SIZE_TEST\n", "    )\n", "    print(cfg.INPUT.MIN_SIZE_TEST, cfg.INPUT.MAX_SIZE_TEST)\n", "    height, width = original_image.shape[:2]\n", "    image = aug.get_transform(original_image).apply_image(original_image)\n", "    image = torch.as_tensor(image.astype(\"float32\").transpose(2, 0, 1))\n", "\n", "    inputs = {\"image\": image, \"height\": height, \"width\": width}\n", "\n", "    # Sample ready\n", "    sample_inputs = [inputs]\n", "    return sample_inputs\n", "\n", "#helper for segmentation model\n", "def inference(model, inputs):\n", "    # use do_postprocess=False so it returns ROI mask\n", "    inst = model.inference(inputs, do_postprocess=False)[0]\n", "    return [{\"instances\": inst}]"]}, {"cell_type": "code", "execution_count": null, "id": "1b7a5658", "metadata": {}, "outputs": [], "source": ["def get_cfg_segm_model():\n", "    path_to_segmentation_config = r'/home/<USER>/Fishial/detectron2/projects/PointRend/configs/InstanceSegmentation/pointrend_rcnn_R_50_FPN_3x_coco.yaml'\n", "    cfg = get_cfg()\n", "    add_pointrend_config(cfg)\n", "    cfg.merge_from_file(path_to_segmentation_config)\n", "    return cfg\n"]}, {"cell_type": "code", "execution_count": null, "id": "7a131681", "metadata": {}, "outputs": [], "source": ["IMAGE_PATH = r'/home/<USER>/Fishial/output/species_53_576c9cb12ac66.w1000.h666.jpg'\n", "SEGMENTATION_FODLER = r'/home/<USER>/Fishial/output/segmentation_export_torchscript'\n", "MODEL_NAME = \"model_segm_21_08_2023.ts\"\n", "#os.makedirs(SEGMENTATION_FODLER, exist_ok=True)\n", "\n", "cfg = get_cfg_segm_model()"]}, {"cell_type": "code", "execution_count": null, "id": "11fc28f3", "metadata": {}, "outputs": [], "source": ["from PIL import Image\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "6238cb1b", "metadata": {}, "outputs": [], "source": ["image_path = r'/home/<USER>/Fishial/output/species_53_576c9cb12ac66.w1000.h666.jpg'\n", "image = read_image(image_path)\n", "image = torch.as_tensor(image.astype(\"float32\").transpose(2, 0, 1))"]}, {"cell_type": "code", "execution_count": null, "id": "12e5eae2", "metadata": {}, "outputs": [], "source": ["full_model = r'/home/<USER>/Fishial/output/model_21_08_2023_test.pt'\n", "model_script = torch.load(full_model)\n", "model_script.eval()\n", "\n", "inputs = get_sample_inputs(IMAGE_PATH)\n", "image = inputs[0][\"image\"]\n", "inputs = [{\"image\": image}]  # remove other unused keys\n", "traceable_model = TracingAdapter(model_script, inputs, inference)\n", "ts_model = torch.jit.trace(traceable_model, (image,))\n", "ts_model.eval()\n", "\n", "frozen_module = torch.jit.freeze(ts_model)\n", "with PathManager.open(os.path.join(SEGMENTATION_FODLER, MODEL_NAME), \"wb\") as f:\n", "    torch.jit.save(frozen_module, f)\n", "dump_torchscript_IR(frozen_module, SEGMENTATION_FODLER)"]}, {"cell_type": "code", "execution_count": null, "id": "f62d5926", "metadata": {}, "outputs": [], "source": ["from torch.utils.mobile_optimizer import optimize_for_mobile\n", "\n", "optimized_torchscript_model = optimize_for_mobile(frozen_module)\n", "optimized_torchscript_model.save(os.path.join(SEGMENTATION_FODLER,'optimized_torchscript_model.pth'))"]}, {"cell_type": "markdown", "id": "ab10982d", "metadata": {}, "source": ["## load raw file from torch script and test it"]}, {"cell_type": "code", "execution_count": null, "id": "655c1dac", "metadata": {}, "outputs": [], "source": ["from PIL import Image\n", "import numpy as np\n", "from torch.nn import functional as F\n", "import cv2\n", "import matplotlib.pyplot as plt\n", "def convert_PIL_to_numpy(image, format):\n", "    \"\"\"\n", "    Convert PIL image to numpy array of target format.\n", "\n", "    Args:\n", "        image (PIL.Image): a PIL image\n", "        format (str): the format of output image\n", "\n", "    Returns:\n", "        (np.n<PERSON>ray): also see `read_image`\n", "    \"\"\"\n", "    if format is not None:\n", "        # PIL only supports RGB, so convert to RGB and flip channels over below\n", "        conversion_format = format\n", "        if format in [\"BGR\", \"YUV-BT.601\"]:\n", "            conversion_format = \"RGB\"\n", "        image = image.convert(conversion_format)\n", "    image = np.asarray(image)\n", "    # PIL squeezes out the channel dimension for \"L\", so make it HWC\n", "    if format == \"L\":\n", "        image = np.expand_dims(image, -1)\n", "\n", "    # handle formats not supported by PIL\n", "    elif format == \"BGR\":\n", "        # flip channels if needed\n", "        image = image[:, :, ::-1]\n", "    elif format == \"YUV-BT.601\":\n", "        image = image / 255.0\n", "        image = np.dot(image, np.array(_M_RGB2YUV).T)\n", "\n", "    return image\n", "\n", "def _apply_exif_orientation(image):\n", "    \"\"\"\n", "    Applies the exif orientation correctly.\n", "\n", "    This code exists per the bug:\n", "      https://github.com/python-pillow/Pillow/issues/3973\n", "    with the function `ImageOps.exif_transpose`. The Pillow source raises errors with\n", "    various methods, especially `tobytes`\n", "\n", "    Function based on:\n", "      https://github.com/wkentaro/labelme/blob/v4.5.4/labelme/utils/image.py#L59\n", "      https://github.com/python-pillow/Pillow/blob/7.1.2/src/PIL/ImageOps.py#L527\n", "\n", "    Args:\n", "        image (PIL.Image): a PIL image\n", "\n", "    Returns:\n", "        (PIL.Image): the PIL image with exif orientation applied, if applicable\n", "    \"\"\"\n", "    _EXIF_ORIENT = 274 \n", "    \n", "    if not hasattr(image, \"getexif\"):\n", "        return image\n", "\n", "    try:\n", "        exif = image.getexif()\n", "    except Exception:  # https://github.com/facebookresearch/detectron2/issues/1885\n", "        exif = None\n", "\n", "    if exif is None:\n", "        return image\n", "\n", "    orientation = exif.get(_EXIF_ORIENT)\n", "\n", "    method = {\n", "        2: Image.FLIP_LEFT_RIGHT,\n", "        3: Image.ROTATE_180,\n", "        4: Image.FLIP_TOP_BOTTOM,\n", "        5: Image.TRANSPOSE,\n", "        6: Image.ROTATE_270,\n", "        7: Image.TRANSVERSE,\n", "        8: Image.ROTATE_90,\n", "    }.get(orientation)\n", "\n", "    if method is not None:\n", "        return image.transpose(method)\n", "    return image\n", "\n", "def read_image(file_name, format=None):\n", "    \"\"\"\n", "    Read an image into the given format.\n", "    Will apply rotation and flipping if the image has such exif information.\n", "\n", "    Args:\n", "        file_name (str): image file path\n", "        format (str): one of the supported image modes in PIL, or \"BGR\" or \"YUV-BT.601\".\n", "\n", "    Returns:\n", "        image (np.n<PERSON>ray):\n", "            an HWC image in the given format, which is 0-255, uint8 for\n", "            supported image modes in PIL or \"BGR\"; float (0-1 for Y) for YUV-BT.601.\n", "    \"\"\"\n", "   \n", "    image = Image.open(file_name)\n", "\n", "    # work around this bug: https://github.com/python-pillow/Pillow/issues/3973\n", "    image = _apply_exif_orientation(image)\n", "    return convert_PIL_to_numpy(image, format)\n", "\n", "def _do_paste_mask(masks, img_h: int, img_w: int):\n", "    \"\"\"\n", "    Args:\n", "        masks: N, 1, H, W\n", "        boxes: N, 4\n", "        img_h, img_w (int):\n", "        skip_empty (bool): only paste masks within the region that\n", "            tightly bound all boxes, and returns the results this region only.\n", "            An important optimization for CPU.\n", "\n", "    Returns:\n", "        if skip_empty == False, a mask of shape (N, img_h, img_w)\n", "        if skip_empty == True, a mask of shape (N, h', w'), and the slice\n", "            object for the corresponding region.\n", "    \"\"\"\n", "    # On GPU, paste all masks together (up to chunk size)\n", "    # by using the entire image to sample the masks\n", "    # Compared to pasting them one by one,\n", "    # this has more operations but is faster on COCO-scale dataset.\n", "    device = masks.device\n", "\n", "    x0_int, y0_int = 0, 0\n", "    x1_int, y1_int = img_w, img_h\n", "    x0, y0, x1, y1 =  torch.Tensor([[0]]), torch.Tensor([[0]]), torch.Tensor([[img_w]]), torch.Tensor([[img_h]])\n", "\n", "    N = masks.shape[0]\n", "\n", "    img_y = torch.arange(y0_int, y1_int, device=device, dtype=torch.float32) + 0.5\n", "    img_x = torch.arange(x0_int, x1_int, device=device, dtype=torch.float32) + 0.5\n", "    img_y = (img_y - y0) / (y1 - y0) * 2 - 1\n", "    img_x = (img_x - x0) / (x1 - x0) * 2 - 1\n", "    # img_x, img_y have shapes (N, w), (N, h)\n", "    gx = img_x[:, None, :].expand(N, img_y.size(1), img_x.size(1))\n", "    gy = img_y[:, :, None].expand(N, img_y.size(1), img_x.size(1))\n", "    grid = torch.stack([gx, gy], dim=3)\n", "\n", "    resized_mask = F.grid_sample(masks, grid.to(masks.dtype), align_corners=False)\n", "\n", "    return resized_mask"]}, {"cell_type": "code", "execution_count": null, "id": "bde50600", "metadata": {}, "outputs": [], "source": ["model_path = r'/home/<USER>/Fishial/output/segmentation_export_torchscript/model_segm_21_08_2023.ts'\n", "model = torch.jit.load(model_path)\n", "model.eval()\n", "model.cpu()"]}, {"cell_type": "code", "execution_count": null, "id": "a449d87c", "metadata": {}, "outputs": [], "source": ["image_path = '/home/<USER>/Fishial/output/dbfded7e-77a4-433e-b1ee-c52a67d8b0fd.jpg'\n", "input_np = read_image(image_path)\n", "input_ts = torch.as_tensor(input_np.astype(\"float32\").transpose(2, 0, 1))\n", "\n", "outputs_ts = model(input_ts) \n", "masks = outputs_ts[2]\n", "device = 'cpu'\n", "\n", "s_t = time.time()\n", "mask_outputs = []\n", "for ind in range(len(outputs_ts[0])):\n", "    x1, y1, x2, y2 = int(outputs_ts[0][ind][0]), int(outputs_ts[0][ind][1]), int(outputs_ts[0][ind][2]) , int(outputs_ts[0][ind][3])  \n", "    img_w, img_h = x2 - x1, y2 - y1\n", "    masks = outputs_ts[2][ind, None, :, :]\n", "    masks_chunk = _do_paste_mask(masks, img_h, img_w)\n", "\n", "    np_mask = masks_chunk.numpy()\n", "    np_mask = np.where(np_mask > 0.5, 255, 0)\n", "    \n", "    crop_image = input_np[y1:y1 + img_h, x1:x1+img_w]\n", "    crop_image = crop_image.astype(int)\n", "    \n", "    np_mask = np_mask.astype(np.uint8)\n", "    \n", "    res = cv2.bitwise_and(crop_image, crop_image, mask = np_mask[0][0])\n", "    mask_outputs.append([crop_image, np_mask[0][0]])\n"]}, {"cell_type": "code", "execution_count": null, "id": "f8b99431", "metadata": {}, "outputs": [], "source": ["plt.imshow(mask_outputs[1][1])\n", "plt.show()\n", "\n", "plt.imshow(mask_outputs[1][0])\n", "plt.show()"]}, {"cell_type": "markdown", "id": "b20f3ce4", "metadata": {}, "source": ["# Classification model"]}, {"cell_type": "code", "execution_count": null, "id": "63f5d3b3", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "# Change path specificly to your directories\n", "sys.path.insert(1, '/home/<USER>/Fishial/Object-Detection-Model')"]}, {"cell_type": "code", "execution_count": null, "id": "e0e1bf13", "metadata": {}, "outputs": [], "source": ["CLASSIFICATION_FODLER = r'/home/<USER>/Fishial/output/classification/resnet_18_triplet_08_09_2023_v06_under_train_cross'\n", "MODEL_SCRIPT_PATH     = os.path.join(CLASSIFICATION_FODLER, 'model.pt')\n", "CKPT_PATH             = os.path.join(CLASSIFICATION_FODLER, 'model.ckpt')\n", "MODEL_NAME_TS = \"model.ts\"\n", "\n", "os.makedirs(CLASSIFICATION_FODLER, exist_ok=True)\n"]}, {"cell_type": "markdown", "id": "ed39748d", "metadata": {}, "source": ["## IF script model wasn't created, create it from state dict \"checkpoint\""]}, {"cell_type": "code", "execution_count": null, "id": "bf3a2b7d", "metadata": {"scrolled": true}, "outputs": [], "source": ["from module.classification_package.src.model import init_model\n", "\n", "model_script = init_model(289, device='cpu', checkpoint = os.path.join(CLASSIFICATION_FODLER, r'model.ckpt'))\n", "model_script.eval()\n", "torch.save(model_script, MODEL_SCRIPT_PATH)\n"]}, {"cell_type": "markdown", "id": "7a31b968", "metadata": {}, "source": ["# Get model from PT"]}, {"cell_type": "code", "execution_count": null, "id": "ea9a82bd", "metadata": {"scrolled": true}, "outputs": [], "source": ["model_script = torch.load(MODEL_SCRIPT_PATH)\n", "model_script.eval()"]}, {"cell_type": "markdown", "id": "0c1970a2", "metadata": {}, "source": ["# convert the script model to torchscript and freeze him "]}, {"cell_type": "code", "execution_count": null, "id": "6a3a5292", "metadata": {}, "outputs": [], "source": ["ts_model = torch.jit.script(model_script)\n", "ts_model.eval()\n", "frozen_module = torch.jit.freeze(ts_model)\n", "with PathManager.open(os.path.join(CLASSIFICATION_FODLER, MODEL_NAME_TS), \"wb\") as f:\n", "    torch.jit.save(frozen_module, f)\n", "    \n", "dump_torchscript_IR(frozen_module, CLASSIFICATION_FODLER)"]}, {"cell_type": "markdown", "id": "95379cbe", "metadata": {}, "source": ["# make some tests, laod and push random input "]}, {"cell_type": "code", "execution_count": null, "id": "c278892a", "metadata": {}, "outputs": [], "source": ["model_ts = torch.jit.load(os.path.join(CLASSIFICATION_FODLER, MODEL_NAME_TS))\n", "model_ts.eval()"]}, {"cell_type": "markdown", "id": "55dfd5a1", "metadata": {}, "source": ["# Lets provide perfomance test and accuracy of optimized model\n", "\n", "frozen torchscript model have to be ~50% faster than script model after warm up and diffrence in output tend to be 0|"]}, {"cell_type": "code", "execution_count": null, "id": "e1d09e75", "metadata": {}, "outputs": [], "source": ["for _ in range(10):\n", "    input_rand = torch.rand(1,3,224,224)\n", "\n", "    s_t = time.time()\n", "    output_src = model_script(input_rand)\n", "    exec_src = time.time() - s_t\n", "\n", "    s_t = time.time()\n", "    output_ts = model_ts(input_rand)\n", "    exec_ts = time.time() - s_t\n", "\n", "    diff_emb = (output_src[0] - output_ts[0]).abs().sum() # diff on a embedding values\n", "    diff_acc = (output_src[1] - output_ts[1]).abs().sum() # diff on a accuracy output\n", "\n", "    print(f\"Execution time: |ts: {exec_ts} vs src: {exec_src}| Difference on outputs: |{diff_emb} vs {diff_acc}|\")"]}, {"cell_type": "markdown", "id": "d0bbce48", "metadata": {}, "source": ["## Test on a real data, reading, converting and inference"]}, {"cell_type": "code", "execution_count": null, "id": "a54e93e4", "metadata": {}, "outputs": [], "source": ["import cv2\n", "import numpy as np\n", "import fiftyone as fo\n", "\n", "from PIL import Image\n", "from torch import nn\n", "from tqdm import tqdm\n", "from torchvision import transforms\n", "\n", "from module.segmentation_package.src.utils import get_mask\n", "from module.classification_package.src.utils import read_json, save_json"]}, {"cell_type": "code", "execution_count": null, "id": "0665cc0a", "metadata": {}, "outputs": [], "source": ["def get_image(img_path, polyline):\n", "    full_image = cv2.imread(img_path)\n", "    mask_np = get_mask(full_image, np.array(polyline))\n", "    mask_pil = Image.fromarray(mask_np)\n", "    mask_tensor = loader(mask_pil)\n", "    \n", "    return mask_pil, mask_tensor.unsqueeze(0) # add batch dimension\n", "\n", "def classify_fc(output):\n", "    acc_values = softmax(output)\n", "    class_id = torch.argmax(acc_values).item()\n", "    #print(f\"Recognized species id {class_id} with liklyhood: {acc_values[0][class_id]}\")\n", "    return class_id, acc_values[0][class_id]\n", "\n", "def classify_embedding(data_base, embedding, indexes_of_elements):\n", "    diff = (data_base - embedding).pow(2).sum(dim=1).sqrt()\n", "    val, indi = torch.sort(diff)\n", "    class_lib = [[indexes_of_elements['list_of_ids'][indiece], diff[indiece]] for indiece in indi[:10]]\n", "    class_lib = [[indexes_of_elements['categories'][str(rec[0][0])],rec[0], rec[1]] for rec in class_lib]\n", "    return class_lib"]}, {"cell_type": "markdown", "id": "82b85107", "metadata": {}, "source": ["## If embedding tensor has been created define it and check accuracy "]}, {"cell_type": "code", "execution_count": null, "id": "98acdd65", "metadata": {}, "outputs": [], "source": ["data_base = torch.load('/home/<USER>/Fishial/output/classification/resnet_18_triplet_08_09_2023_v06_under_train_cross/embeddings.pt')\n", "data_base.cpu()\n", "indexes_of_elements = read_json('/home/<USER>/Fishial/output/classification/resnet_18_triplet_08_09_2023_v06_under_train_cross/idx.json')\n"]}, {"cell_type": "code", "execution_count": null, "id": "e9c0e43e", "metadata": {}, "outputs": [], "source": ["\n", "fo_dataset = fo.load_dataset(\"classification-05-09-2023-v06\")\n", "fo_dataset = fo_dataset.match_tags(['val', 'train'])\n", "\n", "# get softmax function\n", "softmax = nn.Softmax(dim=1)\n", "\n", "loader = transforms.Compose([\n", "        transforms.Resize((224, 224), Image.BILINEAR),\n", "        transforms.To<PERSON><PERSON><PERSON>(),\n", "        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])"]}, {"cell_type": "code", "execution_count": null, "id": "9c90c6e1", "metadata": {}, "outputs": [], "source": ["pbar = tqdm(fo_dataset.match_tags(['val', 'train']))\n", "\n", "acc_fc = []\n", "acc_eb = []\n", "\n", "wrong_classified_by_both = []\n", "\n", "for sample in pbar:\n", "    img_path = sample['filepath']\n", "    label = sample['polyline']['label']\n", "    image_id, annotation_id, drawn_fish_id = sample['image_id'], sample['annotation_id'], sample['drawn_fish_id']\n", "    width, height = sample['width'], sample['height']\n", "    \n", "    polyline = sample['polyline']['points'][0]\n", "    polyline = [[int(point[0] * width), int(point[1] * height)] for point in polyline]\n", "    \n", "    pil_image, input_tensor = get_image(img_path, polyline)   \n", "    output_ts     = model_ts(input_tensor) \n", "    \n", "    class_id_ts, score_ts = classify_fc(output_ts[1])\n", "    output = classify_embedding(data_base, output_ts[0][0], indexes_of_elements)\n", "    \n", "    if output[0][2].item() != 0.0:\n", "        print(\"ERROR, something went wrong in propper way it's impossible\")\n", "        \n", "    acc_fc.append(indexes_of_elements['categories'][str(class_id_ts)]['name'] == label)\n", "    acc_eb.append(output[1][0]['name'] == label)\n", "    if indexes_of_elements['categories'][str(class_id_ts)]['name'] != label and output[1][0]['name'] != label:\n", "        wrong_classified_by_both.append([label, image_id, annotation_id, drawn_fish_id,indexes_of_elements['categories'][str(class_id_ts)]['name'],output[1][0]['name'], output[1][2].item()])\n", "        sample['wrong_emb'] = output[1][0]['name']\n", "        sample['wrong_dist'] = output[1][2].item()\n", "        sample['wrong_fc'] = indexes_of_elements['categories'][str(class_id_ts)]['name']\n", "        sample.save()\n", "    \n", "    pbar.set_description(f\"Eval acc_fc: {sum(acc_fc)/len(acc_fc)} vs {sum(acc_eb)/len(acc_eb)} acc_eb: WRONG: {len(wrong_classified_by_both)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "40a713c7", "metadata": {}, "outputs": [], "source": ["wrong_classified_path = r'/home/<USER>/Fishial/output/classification/resnet_18_triplet_08_09_2023_v06_under_train_cross/wrong_classified.json'\n", "save_json(wrong_classified_by_both, wrong_classified_path)"]}, {"cell_type": "markdown", "id": "e2fb32c1", "metadata": {}, "source": ["## Next code is temporary if wasn't remove please do it"]}, {"cell_type": "code", "execution_count": null, "id": "1dbddb79", "metadata": {}, "outputs": [], "source": ["import torch.nn as nn\n", "import numpy as np\n", "import logging\n", "import torch\n", "import json\n", "\n", "from PIL import Image\n", "from torchvision import transforms\n", "\n", "\n", "def read_json(path_to_json):\n", "    with open(path_to_json) as f:\n", "        return json.load(f)\n", "    \n", "def get_results(output):\n", "        top_1, top_1_val = None, 10e9\n", "        top_median, top_median_val = None, 10e9\n", "\n", "        for i in output:\n", "            if top_1_val > output[i]['top_1']:\n", "                top_1 = i\n", "                top_1_val = output[i]['top_1']\n", "\n", "            if top_median_val > output[i]['median']:\n", "                top_median = i\n", "                top_median_val = output[i]['median']\n", "        return [top_1, top_median]\n", "    \n", "class EmbeddingClassifier:\n", "    def __init__(self, model_path, data_set_path, data_id_path, device='cpu', THRESHOLD = 8.84):\n", "        self.device = device\n", "        self.THRESHOLD = THRESHOLD\n", "        self.indexes_of_elements = read_json(data_id_path)\n", "        self.softmax = nn.Softmax(dim=1)\n", "        \n", "        self.model = torch.jit.load(model_path)\n", "        self.model.eval()\n", "        self.model.to(device)\n", "        \n", "        self.loader = transforms.Compose([\n", "            transforms.Resize((224, 224), Image.BILINEAR),\n", "            transforms.To<PERSON><PERSON><PERSON>(),\n", "            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])\n", "        ])\n", "        \n", "        self.data_base = torch.load(data_set_path).to(device)\n", "        logging.info(\"[INIT][CLASSIFICATION] Initialization of classifier was finished\")\n", "                \n", "    def __inference(self, image, top_k = 15): \n", "        logging.info(\"[PROCESSING][CLASSIFICATION] Getting embedding for a single detection mask\")\n", "        \n", "        dump_embed, fc_output = self.model(image.unsqueeze(0).to(self.device))\n", "        \n", "        logging.info(\"[PROCESSING][CLASSIFICATION] Classification by Full Connected layer for a single detection mask\")  \n", "        classes, scores = self.__classify_fc(fc_output)\n", "        \n", "        logging.info(\"[PROCESSING][CLASSIFICATION] Classification by embedding for a single detection mask\")\n", "        output_by_embeddings = self.__classify_embedding(dump_embed[0], top_k)\n", "        \n", "        logging.info(\"[PROCESSING][CLASSIFICATION] Beautify output for a single detection mask\")\n", "        result = self.__beautifier_output(output_by_embeddings, self.indexes_of_elements['categories'][str(classes[0].item())]['name'])\n", "        result = [[self.__get_species_name(record[0]), record] for record in result]\n", "        return result\n", "    \n", "    def __beautifier_output(self, output_by_embeddings, classification_label):\n", "            \n", "        dict_results = {}\n", "        for i in output_by_embeddings:\n", "            if i[0]['name'] in dict_results:\n", "                dict_results[i[0]['name']]['values'].append(i[2].item())\n", "                dict_results[i[0]['name']]['annotations'].append(i[1])\n", "            else:\n", "                dict_results.update({i[0]['name']: {\n", "                    'values': [i[2].item()],\n", "                    'annotations': [i[1]]\n", "                }})\n", "\n", "        for i in dict_results:\n", "            dict_results[i].update({'top_1': dict_results[i]['values'][0]})\n", "            dict_results[i].update({'annotation': dict_results[i]['annotations'][0]})\n", "            dict_results[i].update({'median': np.median(dict_results[i]['values'])})\n", "            del dict_results[i]['values']\n", "            del dict_results[i]['annotations']\n", "\n", "        labels = get_results(dict_results)\n", "        labels = list(set(labels))\n", "\n", "        for result in list(dict_results.keys()):\n", "            if result not in labels:\n", "                del dict_results[result]\n", "            else:\n", "                mean_distance = (dict_results[result]['top_1'] + dict_results[result]['median'])/2\n", "                dict_results[result]['dist'] = mean_distance\n", "                dict_results[result]['conf'] = round(self.__get_confidence(mean_distance), 3)\n", "                logging.info(f\"[PROCESSING][CLASSIFICATION] the threshold |{mean_distance}| has been recalculated to |{dict_results[result]['conf']}|\")\n", "        results = [[label, dict_results[label]['conf'], dict_results[label]['annotation']] for label in dict_results]\n", "        logging.info(\"[PROCESSING][CLASSIFICATION] Classification by embedding was finished successfuly\")\n", "\n", "        if classification_label not in labels:\n", "            logging.info(\"[PROCESSING][CLASSIFICATION] Append into output classification result by FC - layer\")\n", "            results.append([classification_label, 0.1, [None, None, None]])\n", "        else:\n", "            logging.info(\"[PROCESSING][CLASSIFICATION] Output from FC layer exist in Embedding results\")\n", "        results = sorted(results, key=lambda x: x[1], reverse=True)\n", "        return results\n", "    \n", "    def __get_confidence(self, dist):\n", "        min_dist = 4.2\n", "        max_dist = self.THRESHOLD\n", "        delta = max_dist - min_dist\n", "        return 1.0 - (max(min(max_dist, dist), min_dist) - min_dist) / delta\n", "    \n", "    def inference_numpy(self, img, top_k=10):\n", "        image = Image.fromarray(img)\n", "        image = self.loader(image)\n", "        \n", "        return self.__inference(image, top_k)\n", "    \n", "    def batch_inference(self, imgs):\n", "        batch_input = []\n", "        for idx in range(len(imgs)):\n", "            image = Image.fromarray(imgs[idx])\n", "            image = self.loader(image)\n", "            batch_input.append(image)\n", "\n", "        batch_input = torch.stack(batch_input)\n", "        dump_embeds, class_ids = self.model(batch_input)\n", "        \n", "        logging.info(\"[PROCESSING][CLASSIFICATION] Classification by Full Connected layer for a single detection mask\")  \n", "        classes, scores = self.__classify_fc(class_ids)\n", "       \n", "        outputs = []\n", "        for output_id in range(len(classes)):\n", "\n", "            logging.info(\"[PROCESSING][CLASSIFICATION] Classification by embedding for a single detection mask\")\n", "            output_by_embeddings = self.__classify_embedding(dump_embeds[output_id])\n", "            result = self.__beautifier_output(output_by_embeddings, self.indexes_of_elements['categories'][str(classes[output_id].item())]['name'])\n", "            result = [[self.__get_species_name(record[0]), record] for record in result]\n", "            \n", "            outputs.append(result)\n", "            \n", "        return outputs\n", "    \n", "    def __classify_fc(self, output):\n", "        acc_values = self.softmax(output)\n", "        class_id = torch.argmax(acc_values, dim=1)\n", "        #print(f\"Recognized species id {class_id} with liklyhood: {acc_values[0][class_id]}\")\n", "        return class_id, acc_values\n", "\n", "    def __classify_embedding(self, embedding, top_k = 15):\n", "        diff = (self.data_base - embedding).pow(2).sum(dim=1).sqrt()\n", "        val, indi = torch.sort(diff)\n", "        class_lib = [[self.indexes_of_elements['list_of_ids'][indiece], diff[indiece]] for indiece in indi[:top_k]]\n", "        class_lib = [[self.indexes_of_elements['categories'][str(rec[0][0])],rec[0], rec[1]] for rec in class_lib]\n", "        return class_lib\n", "\n", "    \n", "    def __get_species_name(self, category_name):\n", "        for i in self.indexes_of_elements['categories']:\n", "            if self.indexes_of_elements['categories'][i]['name'] == category_name:\n", "                return self.indexes_of_elements['categories'][i]['species_id']"]}, {"cell_type": "code", "execution_count": null, "id": "6e42f626", "metadata": {}, "outputs": [], "source": ["model_path = '/home/<USER>/Fishial/output/classification/resnet_18_triplet_08_09_2023_v06_under_train_cross/model.ts'\n", "embedding_path = '/home/<USER>/Fishial/output/classification/resnet_18_triplet_05_09_2023/embeddings_new.pt'\n", "index_path  = '/home/<USER>/Fishial/output/classification/resnet_18_triplet_05_09_2023/idx_new_short.json'\n", "\n", "model = EmbeddingClassifier(model_path, embedding_path, index_path)\n"]}], "metadata": {"kernelspec": {"display_name": "NEMO", "language": "python", "name": "nemo"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 5}