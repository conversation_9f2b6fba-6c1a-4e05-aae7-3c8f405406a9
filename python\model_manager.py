#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Model Manager for Fishial AI
模型管理器 - 负责加载和管理AI模型
"""

import os
import json
import yaml
import zipfile
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import hashlib
import time

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelManager:
    """模型管理器类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化模型管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.models = {}
        self.labels = self._load_labels()
        
        # 创建必要的目录
        self._create_directories()
        
        # 解压模型文件
        self._extract_models()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"配置文件加载成功: {self.config_path}")
            return config
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            # 返回默认配置
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'models': {
                'classification': {
                    'model_path': 'models/classification/',
                    'default_model': 'classification_rectangle_v9-3.zip',
                    'input_size': [224, 224],
                    'labels_path': 'labels.json',
                    'confidence_threshold': 0.5
                },
                'detection': {
                    'model_path': 'models/detection/',
                    'model_file': 'detector_v10_m5.zip',
                    'input_size': [640, 640],
                    'confidence_threshold': 0.5
                },
                'segmentation': {
                    'model_path': 'models/segmentation/',
                    'model_file': 'segmentator_fpn_res18_416_1.zip',
                    'input_size': [416, 416],
                    'confidence_threshold': 0.5
                }
            }
        }
    
    def _load_labels(self) -> Dict[str, str]:
        """加载标签文件"""
        try:
            labels_path = self.config.get('models', {}).get('classification', {}).get('labels_path', 'labels.json')
            with open(labels_path, 'r', encoding='utf-8') as f:
                labels = json.load(f)
            logger.info(f"标签文件加载成功: {labels_path}, 共{len(labels)}个类别")
            return labels
        except Exception as e:
            logger.error(f"标签文件加载失败: {e}")
            return {}
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            'models/classification/extracted',
            'models/detection/extracted', 
            'models/segmentation/extracted',
            'logs',
            'uploads'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.debug(f"目录创建/确认: {directory}")
    
    def _extract_models(self):
        """解压模型文件"""
        model_configs = self.config.get('models', {})
        
        for model_type, config in model_configs.items():
            model_path = config.get('model_path', '')
            
            if model_type == 'classification':
                # 处理分类模型（可能有多个）
                model_files = config.get('model_files', [])
                default_model = config.get('default_model', '')
                
                for model_file in model_files:
                    self._extract_single_model(model_path, model_file, model_type)
                    
            else:
                # 处理检测和分割模型
                model_file = config.get('model_file', '')
                if model_file:
                    self._extract_single_model(model_path, model_file, model_type)
    
    def _extract_single_model(self, model_path: str, model_file: str, model_type: str):
        """解压单个模型文件"""
        zip_path = os.path.join(model_path, model_file)
        extract_path = os.path.join(model_path, 'extracted', model_file.replace('.zip', ''))
        
        if not os.path.exists(zip_path):
            logger.warning(f"模型文件不存在: {zip_path}")
            return
            
        if os.path.exists(extract_path) and os.listdir(extract_path):
            logger.info(f"模型已解压: {extract_path}")
            return
            
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_path)
            logger.info(f"模型解压成功: {model_type} - {model_file}")
        except Exception as e:
            logger.error(f"模型解压失败: {model_file} - {e}")
    
    def get_model_path(self, model_type: str, model_name: str = None) -> Optional[str]:
        """
        获取模型路径
        
        Args:
            model_type: 模型类型 (classification, detection, segmentation)
            model_name: 模型名称（可选，用于分类模型）
            
        Returns:
            模型文件路径
        """
        model_config = self.config.get('models', {}).get(model_type, {})
        model_path = model_config.get('model_path', '')
        
        if model_type == 'classification':
            if not model_name:
                model_name = model_config.get('default_model', '')
            extract_path = os.path.join(model_path, 'extracted', model_name.replace('.zip', ''))
        else:
            model_file = model_config.get('model_file', '')
            extract_path = os.path.join(model_path, 'extracted', model_file.replace('.zip', ''))
        
        # 查找模型文件
        if os.path.exists(extract_path):
            for file in os.listdir(extract_path):
                if file.endswith(('.pth', '.pt', '.ts', '.ckpt', '.onnx')):
                    return os.path.join(extract_path, file)
        
        logger.warning(f"未找到模型文件: {model_type} - {model_name}")
        return None
    
    def get_config(self, section: str = None) -> Dict[str, Any]:
        """
        获取配置信息
        
        Args:
            section: 配置节名称（可选）
            
        Returns:
            配置字典
        """
        if section:
            return self.config.get(section, {})
        return self.config
    
    def get_labels(self) -> Dict[str, str]:
        """获取标签字典"""
        return self.labels
    
    def get_model_info(self, model_type: str) -> Dict[str, Any]:
        """
        获取模型信息
        
        Args:
            model_type: 模型类型
            
        Returns:
            模型信息字典
        """
        model_config = self.config.get('models', {}).get(model_type, {})
        model_path = self.get_model_path(model_type)
        
        info = {
            'type': model_type,
            'config': model_config,
            'model_path': model_path,
            'available': model_path is not None,
            'input_size': model_config.get('input_size', [224, 224]),
            'confidence_threshold': model_config.get('confidence_threshold', 0.5)
        }
        
        if model_type == 'classification':
            info['num_classes'] = len(self.labels)
            info['labels'] = self.labels
            
        return info
    
    def list_available_models(self) -> Dict[str, bool]:
        """列出可用的模型"""
        models = {}
        for model_type in ['classification', 'detection', 'segmentation']:
            model_path = self.get_model_path(model_type)
            models[model_type] = model_path is not None
        return models
    
    def validate_models(self) -> Dict[str, Dict[str, Any]]:
        """验证模型完整性"""
        validation_results = {}
        
        for model_type in ['classification', 'detection', 'segmentation']:
            result = {
                'available': False,
                'path': None,
                'size': 0,
                'error': None
            }
            
            try:
                model_path = self.get_model_path(model_type)
                if model_path and os.path.exists(model_path):
                    result['available'] = True
                    result['path'] = model_path
                    result['size'] = os.path.getsize(model_path)
                else:
                    result['error'] = "模型文件不存在"
            except Exception as e:
                result['error'] = str(e)
                
            validation_results[model_type] = result
            
        return validation_results


# 全局模型管理器实例
model_manager = None

def get_model_manager() -> ModelManager:
    """获取全局模型管理器实例"""
    global model_manager
    if model_manager is None:
        model_manager = ModelManager()
    return model_manager


if __name__ == "__main__":
    # 测试模型管理器
    manager = ModelManager()
    
    print("=== 模型管理器测试 ===")
    print(f"可用模型: {manager.list_available_models()}")
    print(f"标签数量: {len(manager.get_labels())}")
    
    # 验证模型
    validation = manager.validate_models()
    for model_type, result in validation.items():
        print(f"{model_type}: {'✓' if result['available'] else '✗'} - {result.get('error', 'OK')}")
