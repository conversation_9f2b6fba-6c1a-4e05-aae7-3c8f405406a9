# Fishial AI Fish Identification - Requirements
# 鱼类识别系统依赖包

# 核心深度学习框架
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# 计算机视觉
opencv-python>=4.8.0
pillow>=10.0.0

# 科学计算
numpy>=1.21.0
scipy>=1.9.0
pandas>=1.5.0

# 图像处理和几何计算
shapely>=2.0.0
scikit-image>=0.19.0

# 配置文件处理
pyyaml>=6.0
python-dotenv>=1.0.0

# Web框架
flask>=2.3.0
werkzeug>=2.3.0
flask-cors>=4.0.0

# HTTP请求
requests>=2.31.0
urllib3>=2.0.0

# 数据序列化
json5>=0.9.0

# 日志和调试
loguru>=0.7.0

# 文件处理
pathlib2>=2.3.0

# 时间处理
python-dateutil>=2.8.0

# 类型检查
typing-extensions>=4.0.0

# 进度条
tqdm>=4.64.0

# 图像增强（可选）
albumentations>=1.3.0

# 模型优化（可选）
onnx>=1.14.0
onnxruntime>=1.15.0

# 数据验证
pydantic>=2.0.0

# 异步支持
aiofiles>=23.0.0

# 内存优化
psutil>=5.9.0
