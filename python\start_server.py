#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fishial AI Server Launcher
服务器启动脚本 - 检查环境并启动API服务
"""

import os
import sys
import time
import logging
import subprocess
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖包"""
    logger.info("检查依赖包...")
    
    required_packages = [
        ("flask", "Flask"),
        ("yaml", "PyYAML"),
        ("PIL", "Pillow"),
        ("numpy", "NumPy")
    ]
    
    missing_packages = []
    
    for module, name in required_packages:
        try:
            __import__(module)
            logger.info(f"✓ {name}")
        except ImportError:
            logger.error(f"✗ {name} 缺失")
            missing_packages.append(name)
    
    # 检查可选包
    optional_packages = [
        ("cv2", "OpenCV"),
        ("torch", "PyTorch")
    ]
    
    for module, name in optional_packages:
        try:
            __import__(module)
            logger.info(f"✓ {name} (可选)")
        except ImportError:
            logger.warning(f"⚠ {name} 缺失 (将使用模拟模式)")
    
    if missing_packages:
        logger.error(f"缺失必需包: {', '.join(missing_packages)}")
        logger.info("请运行: python install_dependencies.py")
        return False
    
    logger.info("依赖检查通过")
    return True

def check_models():
    """检查模型文件"""
    logger.info("检查模型文件...")
    
    model_paths = [
        "models/classification",
        "models/detection", 
        "models/segmentation"
    ]
    
    models_found = 0
    total_models = 0
    
    for model_path in model_paths:
        if os.path.exists(model_path):
            files = list(Path(model_path).glob("*.zip"))
            if files:
                logger.info(f"✓ {model_path}: {len(files)} 个模型文件")
                models_found += len(files)
            else:
                logger.warning(f"⚠ {model_path}: 无模型文件")
            total_models += 1
        else:
            logger.warning(f"⚠ {model_path}: 目录不存在")
    
    if models_found == 0:
        logger.warning("未找到模型文件，将使用模拟模式")
    else:
        logger.info(f"找到 {models_found} 个模型文件")
    
    return True

def check_config():
    """检查配置文件"""
    logger.info("检查配置文件...")
    
    config_file = "config.yaml"
    if os.path.exists(config_file):
        logger.info(f"✓ 配置文件: {config_file}")
        return True
    else:
        logger.warning(f"⚠ 配置文件不存在: {config_file}")
        logger.info("将使用默认配置")
        return True

def check_directories():
    """检查并创建必要目录"""
    logger.info("检查目录结构...")
    
    directories = [
        "uploads",
        "logs",
        "models/classification/extracted",
        "models/detection/extracted",
        "models/segmentation/extracted"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"✓ 目录: {directory}")

def test_api_import():
    """测试API模块导入"""
    logger.info("测试API模块...")
    
    try:
        # 测试导入主要模块
        import fish_api
        logger.info("✓ fish_api 模块导入成功")
        
        # 测试导入自定义模块
        try:
            import model_manager
            import inference_engine
            logger.info("✓ 模型管理模块导入成功")
        except ImportError as e:
            logger.warning(f"⚠ 模型管理模块导入失败: {e}")
            logger.info("将使用基础模式")
        
        return True
        
    except ImportError as e:
        logger.error(f"✗ API模块导入失败: {e}")
        return False

def start_server():
    """启动服务器"""
    logger.info("启动Fishial AI API服务器...")
    
    try:
        # 导入并运行API
        import fish_api
        
        # 这里不直接调用app.run()，而是让用户手动运行
        logger.info("API模块加载成功")
        logger.info("请运行: python fish_api.py")
        return True
        
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        return False

def show_system_info():
    """显示系统信息"""
    print("=" * 60)
    print("🐟 Fishial AI Fish Identification System")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"平台: {sys.platform}")
    print("=" * 60)

def show_usage():
    """显示使用说明"""
    print("\n📖 使用说明:")
    print("1. 安装依赖: python install_dependencies.py")
    print("2. 启动服务: python fish_api.py")
    print("3. 访问API: http://localhost:5001")
    print("4. 查看文档: http://localhost:5001/api/info")
    print("\n🔗 API端点:")
    print("- GET  /health          - 健康检查")
    print("- GET  /species         - 获取鱼种列表")
    print("- POST /identify        - 鱼类识别")
    print("- POST /detect          - 鱼类检测")
    print("- POST /segment         - 鱼类分割")
    print("- GET  /models/status   - 模型状态")

def main():
    """主函数"""
    show_system_info()
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败")
        print("请先运行: python install_dependencies.py")
        return False
    
    # 检查目录
    check_directories()
    
    # 检查配置
    check_config()
    
    # 检查模型
    check_models()
    
    # 测试API导入
    if not test_api_import():
        print("\n❌ API模块测试失败")
        return False
    
    print("\n✅ 系统检查完成")
    show_usage()
    
    # 询问是否启动服务器
    try:
        response = input("\n🚀 是否现在启动服务器? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            print("\n启动服务器...")
            os.system("python fish_api.py")
        else:
            print("\n👋 稍后可以运行: python fish_api.py")
    except KeyboardInterrupt:
        print("\n\n👋 退出")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断")
    except Exception as e:
        logger.error(f"启动脚本异常: {e}")
        print(f"\n❌ 启动失败: {e}")
