{"cells": [{"cell_type": "code", "execution_count": null, "id": "402b2c6b", "metadata": {}, "outputs": [], "source": ["from ultralytics import YOLO"]}, {"cell_type": "code", "execution_count": null, "id": "4149d334", "metadata": {"scrolled": true}, "outputs": [], "source": ["MODEL_PATH = 'best.pt'\n", "model = YOLO(MODEL_PATH)\n"]}, {"cell_type": "code", "execution_count": null, "id": "8e9d927a", "metadata": {}, "outputs": [], "source": ["model.export(format=\"torchscript\")\n", "model.export(format=\"onnx\")  # export the model to ONNX format"]}, {"cell_type": "code", "execution_count": null, "id": "9b604608", "metadata": {"scrolled": true}, "outputs": [], "source": ["model.export(format=\"torchscript\")\n", "model.export(format=\"onnx\")  # export the model to ONNX format"]}, {"cell_type": "code", "execution_count": null, "id": "50200300", "metadata": {}, "outputs": [], "source": ["import onnxruntime\n", "import numpy as np\n", "import torch\n", "\n", "ONNX_MODEL_PATH = '/home/<USER>/<PERSON>/yolov9/UltraSegmTrainFISHIAL_OBJECT_DETECTION/first_test_9e5/weights/best.onnx'\n", "TS_MODEL_PATH   = '/home/<USER>/<PERSON>/yolov9/UltraSegmTrainFISHIAL_OBJECT_DETECTION/first_test_9e5/weights/best.torchscript'\n", "\n", "torch_model = torch.jit.load(TS_MODEL_PATH)\n", "\n", "ort_session = onnxruntime.InferenceSession(ONNX_MODEL_PATH)"]}, {"cell_type": "code", "execution_count": null, "id": "31a12cd7", "metadata": {}, "outputs": [], "source": ["batch_size = 1\n", "x = torch.randn(batch_size, 3, 640, 640, requires_grad=False)\n", "\n", "# compute ONNX Runtime output prediction\n", "ort_inputs = {\n", "    ort_session.get_inputs()[0].name: x.numpy()\n", "}\n", "ort_outs = ort_session.run(None, ort_inputs)[0]\n", "\n", "torch_out = model.model(torch.randn(1, 3, 640, 640))[0]\n", "\n", "full_model_output = torch_out.numpy()\n", "\n", "print(np.sum(ort_outs - full_model_output))\n", "\n", "# compare ONNX Runtime and PyTorch results\n", "np.testing.assert_allclose(ort_outs, full_model_output, rtol=1e-03, atol=1e-05)"]}, {"cell_type": "code", "execution_count": null, "id": "c05f0329", "metadata": {}, "outputs": [], "source": ["ort_session.get_inputs()[0].name"]}, {"cell_type": "code", "execution_count": null, "id": "a8241a37", "metadata": {}, "outputs": [], "source": ["torch_out[0].shape"]}, {"cell_type": "code", "execution_count": null, "id": "0311b865", "metadata": {}, "outputs": [], "source": ["import fiftyone as fo\n", "import cv2\n", "import matplotlib.pyplot as plt\n", "\n", "data = fo.load_dataset('SEGM-2024-V0.8-VALIDATION')\n", "# data = fo.load_dataset('SEGM-2024-V0.8')\n", "\n", "count_to_visualize = 20\n"]}, {"cell_type": "code", "execution_count": null, "id": "1a4e1155", "metadata": {"scrolled": true}, "outputs": [], "source": ["view = data.take(count_to_visualize)\n", "\n", "for sample in data:\n", "    img_path = sample.filepath\n", "    \n", "    result = model.predict(img_path, save=False, imgsz=640, conf=0.5, iou=0.5)\n", "    np_img = cv2.imread(img_path)\n", "    for boxes in result:\n", "        for box in boxes.boxes:\n", "            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)\n", "            cv2.rectangle(np_img,(x1, y1),(x2,y2),(0, 0, 255), 2)\n", "            \n", "    plt.imshow(np_img)\n", "    plt.show()\n", "    \n", "#     break\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "f3af2683", "metadata": {}, "outputs": [], "source": ["img_path = 'for_test.jpg'\n", "result = model.predict(img_path, save=False, imgsz=640, conf=0.1, iou=0.4)\n", "np_img = cv2.imread(img_path)\n", "for boxes in result:\n", "    for box in boxes.boxes:\n", "        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)\n", "        cv2.rectangle(np_img,(x1, y1),(x2,y2),(0, 0, 255), 2)\n", "\n", "plt.imshow(np_img)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "NEMO", "language": "python", "name": "nemo"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 5}