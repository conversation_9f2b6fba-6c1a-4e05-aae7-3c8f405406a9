{"cells": [{"cell_type": "code", "execution_count": 88, "id": "b86172e1", "metadata": {}, "outputs": [], "source": ["import json, os\n", "from os import listdir\n", "from os.path import isfile, join\n", "from fishinternal.src.utils import read_json\n", "from os import walk\n", "import random"]}, {"cell_type": "code", "execution_count": 80, "id": "3ce5e7f4", "metadata": {}, "outputs": [], "source": ["data_json_train = read_json('dataset/data_train.json')\n", "data_json_test = read_json('dataset/data_test.json')\n", "full_data = read_json(\"../fishial_collection/export-fc-2021-11-03.json\")['categories']\n", "full_data = {k['id']:k['supercategory'] for k in full_data if k['name'] == \"General body shape\"}"]}, {"cell_type": "code", "execution_count": 81, "id": "21003f6c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Class:  Balistes capriscus\n", "Class:  Thunnus albacares\n", "Class:  Megalops atlanticus\n", "Class:  <PERSON><PERSON><PERSON><PERSON> falcatus\n", "Class:  <PERSON><PERSON> saxatilis\n", "Class:  <PERSON><PERSON><PERSON>\n", "Class:  <PERSON><PERSON> crysos\n", "Class:  <PERSON><PERSON><PERSON><PERSON> vivanus\n", "Class:  Scomberomorus maculatus\n", "Class:  Rachycentron canadum\n", "Class:  Amphiprion percula\n", "Class:  Haemulon sciurus\n", "Class:  <PERSON><PERSON><PERSON><PERSON> synagris\n", "Class:  <PERSON><PERSON><PERSON><PERSON> campechanus\n", "Class:  Istiophorus albicans\n", "Class:  Cynoscion nebulosus\n", "Class:  Elops saurus\n", "Class:  Carcharias taurus\n", "Class:  <PERSON><PERSON><PERSON><PERSON> griseus\n", "Class:  <PERSON><PERSON><PERSON><PERSON>\n", "Class:  Selene vomer\n", "Class:  <PERSON>p<PERSON><PERSON> striata\n", "Class:  Caranx ruber\n", "Class:  Epinephelus morio\n", "Class:  Amphip<PERSON> ocellaris\n", "Class:  Carcharodon carcharias\n", "Class:  Chaetodipt<PERSON> faber\n", "Class:  Mycteroperca microlepis\n", "Class:  Lagodon rhomboides\n", "Class:  Archosargus probatocephalus\n", "Class:  Lobotes surinamensis\n", "Class:  <PERSON><PERSON><PERSON> gladius\n", "Class:  Pomoxis nigromaculatus\n", "Class:  Sander vitreus\n", "Class:  Rhincodon typus\n", "Class:  Oncorhynchus kisutch\n", "Class:  <PERSON><PERSON><PERSON><PERSON> pelamis\n", "Class:  <PERSON><PERSON><PERSON><PERSON> all<PERSON>us\n", "Class:  <PERSON><PERSON><PERSON><PERSON> analis\n", "Class:  <PERSON>p<PERSON><PERSON> rubrofuscus\n"]}], "source": ["mypath = 'dataset'\n", "min_img_per_class = 9\n", "min_eval_img = 5\n", "max_percent_eva_img = 0.2\n", "\n", "already_preapared = list(set(data_json_train['label_encoded']))\n", "\n", "for i in [x[0] for x in os.walk(mypath)][1:]:\n", "    class_id = os.path.basename(i)\n", "    onlyfiles = [f for f in listdir(i) if isfile(join(i, f))]\n", "    if len(onlyfiles) > min_img_per_class and class_id not in already_preapared:\n", "        eval_count = int(max(5, (len(onlyfiles) * max_percent_eva_img)))\n", "        idx_eval = random.sample(range(len(onlyfiles)), eval_count)\n", "        print(\"Class: \", full_data[int(class_id)])\n", "        for indieces in range(len(onlyfiles)):\n", "            if indieces in idx_eval:\n", "                data_json_test['image_id'].append(os.path.splitext(onlyfiles[indieces])[0])\n", "                data_json_test['label_encoded'].append(class_id)\n", "                data_json_test['label'].append(full_data[int(class_id)])\n", "                data_json_test['img_path'].append(os.path.join(class_id, onlyfiles[indieces]))\n", "            else:\n", "                data_json_train['image_id'].append(os.path.splitext(onlyfiles[indieces])[0])\n", "                data_json_train['label_encoded'].append(class_id)\n", "                data_json_train['label'].append(full_data[int(class_id)])\n", "                data_json_train['img_path'].append(os.path.join(class_id, onlyfiles[indieces]))"]}, {"cell_type": "code", "execution_count": 90, "id": "abfcdf44", "metadata": {}, "outputs": [], "source": ["with open('dataset/data_test.json', 'w') as fp:\n", "    json.dump(data_json_test, fp)\n", "with open('dataset/data_train.json', 'w') as fp:\n", "    json.dump(data_json_train, fp)"]}, {"cell_type": "code", "execution_count": null, "id": "8d652904", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "D2GO", "language": "python", "name": "d2go"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.13"}}, "nbformat": 4, "nbformat_minor": 5}