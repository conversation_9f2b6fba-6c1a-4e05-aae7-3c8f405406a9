{"cells": [{"cell_type": "code", "execution_count": 6, "id": "05fd6789", "metadata": {}, "outputs": [], "source": ["import sys\n", "#Change path specificly to your directories\n", "sys.path.insert(1, '/home/<USER>/Fishial/FishialReaserch')\n", "\n", "import torch\n", "from PIL import Image\n", "import torch\n", "import torch.nn as nn\n", "import torchvision.models as models\n", "import requests\n", "import json\n", "import copy\n", "import cv2\n", "import numpy as np\n", "\n", "from module.classification_package.src.utils import save_json\n", "from module.classification_package.src.utils import read_json\n", "from module.classification_package.src.dataset import FishialDataset\n", "from module.classification_package.src.model import FcNet\n", "from module.classification_package.src.model import Backbone\n", "from module.classification_package.src.model import Model\n", "from module.classification_package.src.loss_functions import TripletLoss, QuadrupletLoss\n", "from module.classification_package.src.train import train\n", "from module.classification_package.src.utils import reverse_norm_image\n", "from module.classification_package.interpreter_classifier import ClassifierFC\n", "\n", "import time\n", "import torch\n", "\n", "\n", "import os\n", "import cv2\n", "\n", "import matplotlib.pyplot as plt\n", "import torchvision.models as models\n", "import numpy as np\n", "from torch import nn\n", "from torch.optim import Optimizer, SGD\n", "from torch.utils.data import DataLoader\n", "from torch.utils.data.dataset import Dataset\n", "from torchvision import transforms\n", "from sklearn.neighbors import KDTree\n", "from PIL import Image\n", "import numpy as np\n", "import random\n", "import sklearn.metrics.pairwise\n", "import scipy.spatial.distance\n", "from sklearn.metrics import confusion_matrix\n", "import copy\n", "import json\n", "import time\n", "import requests\n", "from sklearn.metrics import classification_report\n", "\n", "\n", "import warnings\n", "from sklearn.metrics import cohen_kappa_score\n", "from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay\n", "import logging\n", "import torch\n", "import json\n", "import sys\n", "import numbers\n", "\n", "import numpy as np\n", "from torchvision.transforms.functional import pad\n", "\n", "np.set_printoptions(precision=20)\n", "torch.set_printoptions(precision=20)"]}, {"cell_type": "code", "execution_count": 22, "id": "da7b2c97", "metadata": {}, "outputs": [], "source": ["def predict_dataset(data_set, show = False):\n", "    labels = [data_set.library_name[label]['label'] for label in data_set.library_name]\n", "    test_sd = {data_set_train.library_name[label]['label']:[] for label in data_set_train.library_name}\n", "    y_true = []\n", "    y_pred = []\n", "\n", "    count_true_predicted = 0\n", "\n", "    for i in range(len(data_set)):\n", "        print(\"Left: {}\".format(len(data_set) - i), end='\\r')\n", "        rev_img = reverse_norm_image(data_set[i][0])\n", "        \n", "        true_class = data_set[i][1]\n", "        output = model.inference(data_set[i][0])\n", "        \n", "        y_true.append(int(true_class))    \n", "        y_pred.append(output[0][0])\n", "\n", "        test_sd[labels[int(true_class)]].extend( [labels[match[0]] for match in output[1:4]] )\n", "\n", "        if int(true_class) != output[0][0]:\n", "            if show:\n", "                print(\"P: {} N: {} id: {}\".format(labels[int(true_class)], labels[output[0][0]], data_set.data_frame.iloc[i]['img_path']))\n", "                plt.imshow(rev_img)\n", "                plt.show()\n", "        else:\n", "            count_true_predicted += 1\n", "    \n", "    print('accuracy: {}'.format( (count_true_predicted)/len(data_set)))\n", "    return y_true, y_pred, test_sd"]}, {"cell_type": "code", "execution_count": 3, "id": "ad36d2e5", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["12/02/2021 11:35:11 - INFO - root - Initialization ClassifierFC finished in 0.16 [s]\n"]}], "source": ["model = ClassifierFC('../../../output/final_cross_cross_entropy_0.9896434634974534_223820.0.ckpt')"]}, {"cell_type": "code", "execution_count": 4, "id": "3b359b09", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/d2go-env/lib/python3.6/site-packages/torchvision/transforms/transforms.py:322: UserWarning: Argument interpolation should be of type InterpolationMode instead of int. Please, use InterpolationMode enum.\n", "  \"Argument interpolation should be of type InterpolationMode instead of int. \"\n"]}], "source": ["loader = transforms.Compose([transforms.Resize((224, 224), Image.BILINEAR),\n", "                                    transforms.To<PERSON><PERSON><PERSON>(),\n", "                                    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])\n", "data_set_train = FishialDataset(\n", "        json_path=\"final_train.json\",\n", "        root_folder=\"/home/<USER>/Fishial/FishialReaserch/datasets/cutted_fish\",\n", "        transform=loader\n", "    )\n", "\n", "# data_set_val = FishialDataset(\n", "#         json_path=\"../dataset/data_test_full.json\",\n", "#         root_folder=\"/home/<USER>/Fishial/FishialReaserch/datasets/cutted_fish\",\n", "#         transform=loader\n", "#     )"]}, {"cell_type": "code", "execution_count": 23, "id": "5b90d081", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["P: <PERSON><PERSON><PERSON> hippurus N: Sciaenops ocellatus id: 5/00014304_9968.jpg\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["accuracy: 0.9998304510003391\n"]}], "source": ["y_true, y_pred, test_sd = predict_dataset(data_set_train, show = True)\n", "y_true = np.array(y_true)\n", "y_pred = np.array(y_pred)"]}, {"cell_type": "code", "execution_count": null, "id": "3c916f59", "metadata": {}, "outputs": [], "source": ["cohen_kappa_score(y_true, y_pred)\n", "labels = [data_set_train.library_name[label]['label'] for label in data_set_train.library_name]"]}, {"cell_type": "code", "execution_count": null, "id": "3a404731", "metadata": {}, "outputs": [], "source": ["dict_to_save = classification_report(y_true, y_pred, target_names=labels, output_dict=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ac32caec", "metadata": {}, "outputs": [], "source": ["save_json(data = dict_to_save, path = 'output/cross_entropy_best.json')"]}, {"cell_type": "code", "execution_count": null, "id": "67977bae", "metadata": {}, "outputs": [], "source": ["labels = [data_set_train.library_name[i]['label'] for i in data_set_train.library_name]\n", "cm = confusion_matrix(y_true, y_pred, normalize='true')\n", "fig, ax = plt.subplots(figsize=(30, 30))\n", "disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=labels)\n", "disp = disp.plot(cmap=plt.cm.Blues, ax=ax, xticks_rotation=90)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "30bf4a32", "metadata": {}, "outputs": [], "source": ["classification_report(y_true, y_pred, target_names=labels, output_dict=False)"]}, {"cell_type": "code", "execution_count": null, "id": "4e30675f", "metadata": {}, "outputs": [], "source": ["transform=transforms.Compose([transforms.Resize((224, 224), Image.BILINEAR),\n", "                                  transforms.RandomHorizontalFlip(),\n", "                                  transforms.RandomVerticalFlip(),\n", "                              transforms.RandomRotation(degrees=(0, 90)),\n", "                                    transforms.To<PERSON><PERSON><PERSON>(),\n", "                                    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])"]}, {"cell_type": "code", "execution_count": null, "id": "94242ca3", "metadata": {}, "outputs": [], "source": ["data_main = read_json('../dataset/data_test_full.json')"]}, {"cell_type": "code", "execution_count": null, "id": "374193a9", "metadata": {}, "outputs": [], "source": ["y_test = y_true\n", "y_score = y_pred\n", "# Compute ROC curve and ROC area for each class\n", "fpr = dict()\n", "tpr = dict()\n", "roc_auc = dict()\n", "n_classes = len(y_test[0])\n", "for i in range(n_classes):\n", "    fpr[i], tpr[i], _ = roc_curve(y_test[:, i], y_score[:, i])\n", "    roc_auc[i] = auc(fpr[i], tpr[i])\n", "    print(i, roc_auc[i])\n", "\n", "# Compute micro-average ROC curve and ROC area\n", "fpr[\"micro\"], tpr[\"micro\"], _ = roc_curve(y_test.ravel(), y_score.ravel())\n", "roc_auc[\"micro\"] = auc(fpr[\"micro\"], tpr[\"micro\"])\n", "\n", "# Plot of a ROC curve for a specific class\n", "plt.figure()\n", "plt.plot(fpr[27], tpr[27], label='ROC curve (area = %0.2f)' % roc_auc[27])\n", "plt.plot([0, 1], [0, 1], 'k--')\n", "plt.xlim([0.0, 1.0])\n", "plt.ylim([0.0, 1.05])\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('Receiver operating characteristic example')\n", "plt.legend(loc=\"lower right\")\n", "plt.show()\n", "\n", "# Plot ROC curve\n", "plt.figure()\n", "plt.plot(fpr[\"micro\"], tpr[\"micro\"],\n", "         label='micro-average ROC curve (area = {0:0.2f})'\n", "               ''.format(roc_auc[\"micro\"]))\n", "for i in range(n_classes):\n", "    plt.plot(fpr[i], tpr[i], label='ROC curve of class {0} (area = {1:0.2f})'\n", "                                   ''.format(i, roc_auc[i]))\n", "\n", "plt.plot([0, 1], [0, 1], 'k--')\n", "plt.xlim([0.0, 1.0])\n", "plt.ylim([0.0, 1.05])\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('Some extension of Receiver operating characteristic to multi-class')\n", "plt.legend(loc=\"lower right\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 11, "id": "b0529511", "metadata": {}, "outputs": [], "source": ["labels = {data_set_train.library_name[label]['label']:[] for label in data_set_train.library_name}"]}, {"cell_type": "code", "execution_count": 43, "id": "3db5578a", "metadata": {"scrolled": false}, "outputs": [], "source": ["from collections import Counter\n", "\n", "most_sim_dict = {}\n", "for i in test_sd:\n", "    \n", "    n_max = 6\n", "    keyss = list(Counter(test_sd[i]).keys()) # equals to list(set(words))\n", "    conts = np.array(list(Counter(test_sd[i]).values())) # counts the elements' frequency\n", "    idx = conts.argsort()[-n_max:][::-1]\n", "#     print(10*'*')\n", "#     print(\"main name: \",i,len(test_sd[i]))\n", "#     print([[conts[ind], round((conts[ind]/len(test_sd[i]))*100, 3)] for ind in idx])\n", "    most_sim_dict.update({\n", "        i: [keyss[ind] for ind in idx if round((conts[ind]/len(test_sd[i]))*100, 3) > 5]\n", "    })"]}], "metadata": {"kernelspec": {"display_name": "D2GO", "language": "python", "name": "d2go"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.13"}}, "nbformat": 4, "nbformat_minor": 5}