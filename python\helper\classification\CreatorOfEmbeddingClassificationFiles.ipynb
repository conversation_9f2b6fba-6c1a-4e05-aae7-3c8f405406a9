{"cells": [{"cell_type": "code", "execution_count": null, "id": "dbbd4030", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.insert(1, '/home/<USER>/Fishial/Object-Detection-Model')\n", "import argparse\n", "import cv2\n", "import os\n", "import random\n", "\n", "import numpy as np\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "\n", "import yaml\n", "import torch\n", "from PIL import Image\n", "from torchvision import transforms\n", "from torch import nn\n", "from tqdm import tqdm\n", "\n", "import fiftyone as fo\n", "import fiftyone.zoo as foz\n", "from module.classification_package.src.utils import read_json, save_json\n", "from module.classification_package.src.dataset import FishialDatasetFoOnlineCuting\n", "from module.classification_package.src.model import init_model\n", "from module.segmentation_package.src.utils import get_mask\n", "\n", "import torchvision.models as models\n"]}, {"cell_type": "code", "execution_count": null, "id": "b9e9ac6c", "metadata": {}, "outputs": [], "source": ["def get_image(img_path, polyline):\n", "    full_image = cv2.imread(img_path)\n", "    mask_np = get_mask(full_image, np.array(polyline))\n", "    mask_pil = Image.fromarray(mask_np)\n", "    mask_tensor = loader(mask_pil)\n", "    \n", "    return mask_pil, mask_tensor.unsqueeze(0) # add batch dimension"]}, {"cell_type": "code", "execution_count": null, "id": "33134594", "metadata": {}, "outputs": [], "source": ["loader = transforms.Compose([\n", "        transforms.Resize((224, 224), Image.BILINEAR),\n", "        transforms.To<PERSON><PERSON><PERSON>(),\n", "        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])"]}, {"cell_type": "code", "execution_count": null, "id": "a46ba328", "metadata": {}, "outputs": [], "source": ["EPXPORT_PATH = r'/home/<USER>/Fishial/dataset/export_07_09_2023/06_export_Verified_ALL.json'\n", "# data_export = read_json(EPXPORT_PATH)\n", "\n", "# category_to_species_id = {data['supercategory']: data['fishial_extra']['species_id']\n", "#     for data in data_export['categories'] if data['name'] == 'General body shape' \n", "# }"]}, {"cell_type": "markdown", "id": "349c667b", "metadata": {}, "source": ["Labels have to be like this: \n", "\n", "```json\n", "{\"0\": {\"label_name\": \"<PERSON><PERSON><PERSON><PERSON> ocellatus\", \"species_id\": \"92971e92-c01b-4f19-8189-482eac40ceac\"},\n", " \"1\": {\"label_name\": \"Centropomus undecimalis\", \"species_id\": \"5c2ec367-99ec-4e67-9edd-735eeff36157\"},\n", " ...\n", "}\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "7af525de", "metadata": {"scrolled": true}, "outputs": [], "source": ["MODEL_PATH = r'/home/<USER>/Fishial/output/classification/resnet_18_triplet_08_09_2023_v06_under_train_cross/model.ts'\n", "model = torch.jit.load(MODEL_PATH)\n", "model.eval()\n", "\n", "labels = read_json('/home/<USER>/Fishial/output/classification/resnet_18_triplet_08_09_2023_v06_under_train_cross/categories.json')"]}, {"cell_type": "code", "execution_count": null, "id": "218fef55", "metadata": {}, "outputs": [], "source": ["labels = {\n", "    labels[idx]['label_name']: {\n", "        'internal_id': int(idx),\n", "        'species_id': labels[idx]['species_id']\n", "    } for idx in labels}\n"]}, {"cell_type": "code", "execution_count": null, "id": "012fd778", "metadata": {}, "outputs": [], "source": ["fo_dataset = fo.load_dataset(\"classification-05-09-2023-v06\")\n", "fo_dataset = fo_dataset.match_tags(['val', 'train'])"]}, {"cell_type": "markdown", "id": "ce34ac42", "metadata": {}, "source": ["### Let's create the list of description's of each elements which correspond of position in embeddig tensor list.\n", "\n", "the discription have to be in next shape:\n", "\n", "internal_id, image_id, annotation_id, drawn_fish_id"]}, {"cell_type": "code", "execution_count": null, "id": "02d04f82", "metadata": {"scrolled": true}, "outputs": [], "source": ["data_set_ids = []\n", "embedding_tensor = []\n", "\n", "pbar = tqdm(fo_dataset)\n", "\n", "for sample in pbar:\n", "    img_path = sample['filepath']\n", "    label = sample['polyline']['label']\n", "    image_id, annotation_id, drawn_fish_id = sample['image_id'], sample['annotation_id'], sample['drawn_fish_id']\n", "    width, height = sample['width'], sample['height']\n", "    \n", "    polyline = sample['polyline']['points'][0]\n", "    polyline = [[int(point[0] * width), int(point[1] * height)] for point in polyline]\n", "    \n", "    pil_image, input_tensor = get_image(img_path, polyline) \n", "    \n", "    output = model(input_tensor)[0]\n", "    embedding_tensor.append(output[0].detach())\n", "    \n", "    data_set_ids.append([labels[label]['internal_id'], image_id, annotation_id, drawn_fish_id])"]}, {"cell_type": "code", "execution_count": null, "id": "665881d1", "metadata": {}, "outputs": [], "source": ["#define the root rirectorhe where will be located tensor file and json \n", "absolute_path = '/home/<USER>/Fishial/output/classification/resnet_18_triplet_08_09_2023_v06_under_train_cross'\n", "\n", "#converting dict keys, name to the string name \n", "id_to_name = {\n", "    labels[label]['internal_id']:{\n", "        'name': label,\n", "        'species_id': labels[label]['species_id']\n", "    } for label in labels}\n", "\n", "#combaine categories and list of descriptions\n", "final_data = {\n", "    'categories': id_to_name,x\n", "    'list_of_ids': data_set_ids\n", "}\n", "#stack List[Tensors(256)] to Tensor(N, 256)\n", "data_set = torch.stack(embedding_tensor)\n", "#Save all of them\n", "torch.save(data_set, os.path.join(absolute_path, 'embeddings.pt'))\n", "save_json(final_data, os.path.join(absolute_path, 'idx.json'))"]}], "metadata": {"kernelspec": {"display_name": "NEMO", "language": "python", "name": "nemo"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 5}