{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# MNIST Embeddings\n", "-------------"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Imports and Hyperparameters\n", "---------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "#Change path specificly to your directories\n", "sys.path.insert(1, '/home/<USER>/Fishial/FishialReaserch')\n", "\n", "import torch\n", "import torch.nn as nn\n", "import os\n", "import numpy as np\n", "from torchvision import transforms\n", "from torchvision import models\n", "from module.classification_package.src.dataset import FishialDataset\n", "from module.classification_package.src.model import EmbeddingModel, Backbone, Model\n", "\n", "# os.chdir('..'); \n", "print(os.getcwd())\n", "\n", "from module.classification_package.deep_features import DeepFeatures\n", "BATCH_SIZE = 128\n", "DATA_FOLDER = r'/home/<USER>/Fishial/FishialReaserch/datasets/cutted_fish'\n", "IMGS_FOLDER = 'Outputs/MNIST/Images'\n", "EMBS_FOLDER = 'Outputs/MNIST/Embeddings'\n", "TB_FOLDER = 'Outputs/Tensorboard'\n", "EXPERIMENT_NAME = 'FISHIAL_RESNET18'\n", "DEVICE = 'cpu'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Dataloader\n", "----------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stack(tensor, times=3):\n", "    return(torch.cat([tensor]*times, dim=0))\n", "\n", "\n", "\n", "tfs = transforms.Compose([\n", "            transforms.<PERSON><PERSON><PERSON>([224, 224]),\n", "            transforms.To<PERSON><PERSON><PERSON>(),\n", "            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])\n", "        ])\n", "\n", "FISHIAL_data = FishialDataset(\n", "        json_path=\"data_train.json\",\n", "        root_folder=DATA_FOLDER,\n", "        transform=tfs)\n", "\n", "data_loader = torch.utils.data.DataLoader(FISHIAL_data,\n", "                                          batch_size=BATCH_SIZE,\n", "                                          shuffle=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize Pretrained Model\n", "-------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["resnet18 = models.resnet18(pretrained=True)\n", "resnet18.fc = nn.Identity()\n", "\n", "backbone = Backbone(resnet18)\n", "embedding_model = EmbeddingModel(backbone)\n", "embedding_model.load_state_dict(torch.load('/home/<USER>/Fishial/output/final_qudruplet_cross_entropy_0.95_130400.0.ckpt'))\n", "embedding_model.eval()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize Tensorboard Logging Class\n", "--------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DF = DeepFeatures(model = embedding_model, \n", "                  imgs_folder = IMGS_FOLDER, \n", "                  embs_folder = EMBS_FOLDER, \n", "                  tensorboard_folder = TB_FOLDER, \n", "                  experiment_name=EXPERIMENT_NAME)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Write Embeddings to Tensorboard\n", "------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_preds = []\n", "all_label = {}\n", "data_labels = FISHIAL_data.library_name\n", "for batch in data_loader:\n", "    batch_imgs, labels = batch\n", "    llabels = [data_labels[i]['label'] for i in labels.detach().numpy()]\n", "    labels_name = DF.write_embeddings(x = batch_imgs.to(DEVICE))\n", "    print(len(labels_name) == len(llabels))\n", "    for i in range(len(labels_name)):\n", "        all_label.update({\n", "            labels_name[i]: llabe<PERSON>[i]\n", "        })\n", "    print(len(llabels))\n", "DF.create_tensorboard_log(all_label)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "D2GO", "language": "python", "name": "d2go"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.13"}}, "nbformat": 4, "nbformat_minor": 4}