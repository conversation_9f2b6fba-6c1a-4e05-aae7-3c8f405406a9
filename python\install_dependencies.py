#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fishial AI Dependencies Installer
依赖包安装脚本 - 自动安装所需的Python包
"""

import os
import sys
import subprocess
import platform
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error(f"Python版本过低: {version.major}.{version.minor}")
        logger.error("需要Python 3.8或更高版本")
        return False
    
    logger.info(f"Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True

def get_system_info():
    """获取系统信息"""
    system = platform.system()
    machine = platform.machine()
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}"
    
    logger.info(f"操作系统: {system}")
    logger.info(f"架构: {machine}")
    logger.info(f"Python版本: {python_version}")
    
    return system, machine, python_version

def install_package(package_name, upgrade=False):
    """安装单个包"""
    try:
        cmd = [sys.executable, "-m", "pip", "install"]
        if upgrade:
            cmd.append("--upgrade")
        cmd.append(package_name)
        
        logger.info(f"安装包: {package_name}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logger.info(f"✓ {package_name} 安装成功")
            return True
        else:
            logger.error(f"✗ {package_name} 安装失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"✗ {package_name} 安装超时")
        return False
    except Exception as e:
        logger.error(f"✗ {package_name} 安装异常: {e}")
        return False

def install_pytorch():
    """安装PyTorch"""
    system, machine, python_version = get_system_info()
    
    logger.info("开始安装PyTorch...")
    
    # 根据系统选择合适的PyTorch版本
    if system == "Windows":
        if "cuda" in subprocess.run([sys.executable, "-c", "import torch; print(torch.cuda.is_available())"], 
                                   capture_output=True, text=True).stdout.lower():
            # 如果已有CUDA版本的PyTorch，保持不变
            pytorch_cmd = "torch torchvision torchaudio"
        else:
            # CPU版本
            pytorch_cmd = "torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    else:
        # Linux/Mac
        pytorch_cmd = "torch torchvision torchaudio"
    
    return install_package(pytorch_cmd)

def install_basic_packages():
    """安装基础包"""
    basic_packages = [
        "numpy>=1.21.0",
        "pillow>=10.0.0", 
        "opencv-python>=4.8.0",
        "scipy>=1.9.0",
        "pandas>=1.5.0",
        "pyyaml>=6.0",
        "flask>=2.3.0",
        "werkzeug>=2.3.0",
        "requests>=2.31.0"
    ]
    
    logger.info("安装基础包...")
    success_count = 0
    
    for package in basic_packages:
        if install_package(package):
            success_count += 1
    
    logger.info(f"基础包安装完成: {success_count}/{len(basic_packages)}")
    return success_count == len(basic_packages)

def install_optional_packages():
    """安装可选包"""
    optional_packages = [
        "flask-cors>=4.0.0",
        "python-dotenv>=1.0.0",
        "loguru>=0.7.0",
        "tqdm>=4.64.0",
        "psutil>=5.9.0",
        "pydantic>=2.0.0"
    ]
    
    logger.info("安装可选包...")
    success_count = 0
    
    for package in optional_packages:
        if install_package(package):
            success_count += 1
        # 可选包失败不影响继续
    
    logger.info(f"可选包安装完成: {success_count}/{len(optional_packages)}")
    return True

def verify_installation():
    """验证安装"""
    logger.info("验证安装...")
    
    test_imports = [
        ("numpy", "NumPy"),
        ("PIL", "Pillow"),
        ("cv2", "OpenCV"),
        ("flask", "Flask"),
        ("yaml", "PyYAML"),
        ("requests", "Requests")
    ]
    
    success_count = 0
    for module, name in test_imports:
        try:
            __import__(module)
            logger.info(f"✓ {name} 导入成功")
            success_count += 1
        except ImportError:
            logger.error(f"✗ {name} 导入失败")
    
    # 测试PyTorch（可选）
    try:
        import torch
        logger.info(f"✓ PyTorch 导入成功 (版本: {torch.__version__})")
        success_count += 1
    except ImportError:
        logger.warning("⚠ PyTorch 导入失败（将使用模拟模式）")
    
    logger.info(f"验证完成: {success_count}/{len(test_imports) + 1}")
    return success_count >= len(test_imports)  # PyTorch是可选的

def create_directories():
    """创建必要的目录"""
    directories = [
        "models/classification/extracted",
        "models/detection/extracted",
        "models/segmentation/extracted", 
        "logs",
        "uploads"
    ]
    
    logger.info("创建目录结构...")
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"✓ 目录创建: {directory}")

def main():
    """主函数"""
    print("=" * 60)
    print("🐟 Fishial AI Dependencies Installer")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 获取系统信息
    get_system_info()
    
    # 升级pip
    logger.info("升级pip...")
    install_package("pip", upgrade=True)
    
    # 安装基础包
    if not install_basic_packages():
        logger.error("基础包安装失败，请检查网络连接和权限")
        sys.exit(1)
    
    # 安装PyTorch
    if not install_pytorch():
        logger.warning("PyTorch安装失败，将使用模拟模式")
    
    # 安装可选包
    install_optional_packages()
    
    # 创建目录
    create_directories()
    
    # 验证安装
    if verify_installation():
        print("=" * 60)
        print("✅ 依赖安装完成！")
        print("🚀 现在可以运行: python fish_api.py")
        print("=" * 60)
    else:
        print("=" * 60)
        print("⚠️  部分依赖安装失败，但基础功能可用")
        print("🚀 可以尝试运行: python fish_api.py")
        print("=" * 60)

if __name__ == "__main__":
    main()
