{"cells": [{"cell_type": "code", "execution_count": null, "id": "97db97da", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "id": "74bed40c", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import fiftyone as fo\n", "\n", "from PIL import Image\n", "from torchvision import transforms\n", "\n", "from datetime import datetime\n", "import random\n", "import torch\n", "import lightning as L\n", "from tqdm import tqdm\n", "\n", "from lightning.pytorch.callbacks import ModelCheckpoint, EarlyStopping, LearningRateMonitor\n", "from lightning.pytorch.loggers import TensorBoardLogger\n", "from lightning.pytorch.callbacks import LearningRateFinder\n", "\n", "from lightning.pytorch.utilities.model_summary import ModelSummary\n", "from lightning.pytorch.tuner import Tuner\n", "\n", "from torch.utils.data import DataLoader\n", "\n", "from model.model_light import FishSeg\n", "from model.dataset import SimpleFishialFishDataset\n", "from model.utils import *\n", "from argparse import ArgumentParser\n", "\n", "from model.dataset import FishialSegmentDatasetFoOnlineCuting\n"]}, {"cell_type": "code", "execution_count": 2, "id": "e36676f7", "metadata": {}, "outputs": [], "source": ["MODEL_PATH = '/home/<USER>/Andrew/MMLAB/FishialSEGM/FPN_resnet18_416_2024_07_30_10_54/checkpoints/sample-epoch=82-valid_img_iou=0.95345.ckpt'\n", "\n", "\n", "encoder_type = \"FPN\"\n", "backbone = \"resnet18\"\n", "in_channels = 3\n", "out_classes = 1\n", "\n", "IMAGE_SIZE = 416"]}, {"cell_type": "code", "execution_count": null, "id": "49acef14", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b461c952", "metadata": {}, "outputs": [], "source": ["fo_dataset = fo.load_dataset('SEGM-2024-V0.8')\n", "\n", "train_view = fo_dataset.match(~fo.ViewField(\"tags\").contains('val'))\n", "val_view = fo_dataset.match_tags(\"val\")\n", "\n", "train_dataset = FishialSegmentDatasetFoOnlineCuting(train_view, aug = False, image_size = IMAGE_SIZE)\n", "valid_dataset = FishialSegmentDatasetFoOnlineCuting(val_view, aug = False, image_size = IMAGE_SIZE)"]}, {"cell_type": "code", "execution_count": null, "id": "d5ddc332", "metadata": {}, "outputs": [], "source": ["model = FishSeg(encoder_type, backbone, in_channels=3, out_classes=1, load_checkpoint = MODEL_PATH)\n", "model.eval()\n", "model.cpu()"]}, {"cell_type": "code", "execution_count": null, "id": "15853c48", "metadata": {}, "outputs": [], "source": ["iumage = train_dataset[0]['src_img']\n", "plt.imshow(iumage)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "6553e98d", "metadata": {}, "outputs": [], "source": ["data_pred = np.array(data_pred)\n", "np.save('data_iou_idx.npy', data_pred)"]}, {"cell_type": "code", "execution_count": null, "id": "2060cd94", "metadata": {"scrolled": false}, "outputs": [], "source": ["count_to_visualize = 10\n", "indices = random.sample(range(0,len(train_dataset)), count_to_visualize)\n", "sample_id = 21163\n", "\n", "data_pred = [[],[]]\n", "for sample_id in tqdm(range(len(train_dataset))):\n", "    \n", "    sample = train_dataset[sample_id]\n", "    \n", "    gt_mask = sample['mask'][0]\n", "    \n", "    height, width, _ = sample['src_img'].shape\n", "    \n", "    x_tensor = sample['image'].unsqueeze(0)\n", "    \n", "    with torch.no_grad():\n", "        logits = model(x_tensor)\n", "        \n", "    pr_mask = logits.sigmoid()[0][0].numpy()\n", "    \n", "#     pr_mask = resize_logits_mask_pil(pr_mask, width, height)\n", "#     gt_mask = resize_logits_mask_pil(gt_mask, width, height)\n", "\n", "    intersection = np.logical_and(pr_mask, gt_mask)\n", "    union = np.logical_or(pr_mask, gt_mask)\n", "    iou = np.sum(intersection) / np.sum(union)\n", "    \n", "    if iou < 0.5:\n", "        print(iou, sample_id)\n", "    \n", "    data_pred[0].append(iou)\n", "    data_pred[1].append(sample_id)\n", "\n", "#     print(f\"IoU: {iou}\")\n", "\n", "#     visualize(\n", "#         image=sample['src_img'], \n", "#         ground_truth_mask=gt_mask, \n", "#         predicted=pr_mask\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "id": "05bed862", "metadata": {}, "outputs": [], "source": ["croped_model = model.model\n", "croped_model.eval()\n", "croped_model.cpu()\n", "\n", "example_forward_input = torch.randn(1, 3, IMAGE_SIZE, IMAGE_SIZE)\n", "\n", "# Trace a specific method and construct `ScriptModule` with\n", "# a single `forward` method\n", "module = torch.jit.trace(croped_model.forward, example_forward_input)"]}, {"cell_type": "code", "execution_count": null, "id": "8cfe14b7", "metadata": {}, "outputs": [], "source": ["import torch.onnx\n", "\n", "# Define the path to save the ONNX model\n", "onnx_model_path = f\"saved_models/segmentator/model_resnet18_{IMAGE_SIZE}.onnx\"\n", "\n", "# Load a pre-trained model (in this case, ResNet18)\n", "# model = models.resnet18(pretrained=True)\n", "# model.eval()  # Set the model to evaluation mode\n", "\n", "# Create an input tensor with the corresponding shape (batch_size, channels, height, width)\n", "dummy_input = torch.randn(1, 3, IMAGE_SIZE, IMAGE_SIZE)\n", "\n", "# Export the model to ONNX format\n", "torch.onnx.export(croped_model,              # The model to export\n", "                  dummy_input,               # The input tensor\n", "                  onnx_model_path,           # The path to save the model\n", "                  export_params=True,        # Export the parameters as well\n", "                  opset_version=11,          # The ONNX version\n", "                  do_constant_folding=False, # Disable constant folding optimization\n", "                  input_names=['input'],     # The names of the input layers\n", "                  output_names=['output'],   # The names of the output layers\n", "                  dynamic_axes={'input': {0: 'batch_size'},   # Dynamic batch size\n", "                                'output': {0: 'batch_size'}})\n", "\n", "print(f\"Model successfully exported to {onnx_model_path}\")"]}, {"cell_type": "code", "execution_count": null, "id": "f8009543", "metadata": {}, "outputs": [], "source": ["SAVE_PATH = f\"saved_models/segmentator/model_resnet18_{IMAGE_SIZE}.ts\"\n", "module.save(SAVE_PATH)"]}, {"cell_type": "code", "execution_count": null, "id": "0653df55", "metadata": {}, "outputs": [], "source": ["new_model = torch.jit.load(SAVE_PATH)"]}, {"cell_type": "code", "execution_count": null, "id": "6cabe900", "metadata": {}, "outputs": [], "source": ["import onnxruntime\n", "ort_session = onnxruntime.InferenceSession(onnx_model_path)"]}, {"cell_type": "code", "execution_count": null, "id": "3afdf353", "metadata": {}, "outputs": [], "source": ["batch_size = 1\n", "x = torch.randn(batch_size, 3, IMAGE_SIZE, IMAGE_SIZE, requires_grad=False)\n", "\n", "# compute ONNX Runtime output prediction\n", "ort_inputs = {ort_session.get_inputs()[0].name: x.numpy()}\n", "ort_outs = ort_session.run(None, ort_inputs)\n", "\n", "# with torch.no_grad():\n", "torch_out = croped_model(x)\n", "\n", "np_onnx_oputput = np.array(ort_outs)\n", "full_model_output = torch_out.detach().numpy()\n", "\n", "print(np.sum(np_onnx_oputput - full_model_output))\n", "# # compare ONNX Runtime and PyTorch results\n", "# np.testing.assert_allclose(full_model_output, np_onnx_oputput, rtol=1e-03, atol=1e-05)"]}, {"cell_type": "code", "execution_count": null, "id": "7a7556f7", "metadata": {}, "outputs": [], "source": ["# model.eval()\n", "new_model.eval()"]}, {"cell_type": "code", "execution_count": null, "id": "76fd0ed0", "metadata": {}, "outputs": [], "source": ["loader = transforms.Compose([\n", "            transforms.Resize((IMAGE_SIZE, IMAGE_SIZE), Image.BILINEAR),\n", "            transforms.To<PERSON><PERSON><PERSON>(),\n", "            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])\n", "        ])\n"]}, {"cell_type": "code", "execution_count": null, "id": "bef7a4f7", "metadata": {}, "outputs": [], "source": ["x_tensor"]}, {"cell_type": "code", "execution_count": null, "id": "815a8e43", "metadata": {"scrolled": false}, "outputs": [], "source": ["count_to_visualize = 10\n", "view = data.take(count_to_visualize)\n", "\n", "for sample_id, sample in enumerate(view):\n", "    \n", "    filepath = sample.filepath\n", "    polygon = sample.polyline.points[0]\n", "    \n", "    pil_image = Image.open(filepath)\n", "    width, height = pil_image.size\n", "    \n", "    gt_mask = create_mask(polygon, height, width, color = (100,123,234))\n", "    \n", "    x_tensor = loader(pil_image).unsqueeze(0)\n", "    \n", "    with torch.no_grad():\n", "        logits = new_model(x_tensor)\n", "        logits_std = model(x_tensor)\n", "        logits_croped = croped_model(x_tensor)\n", "\n", "        ort_inputs = {ort_session.get_inputs()[0].name: x_tensor.numpy()}\n", "        ort_outs = torch.tensor(ort_session.run(None, ort_inputs)[0])\n", "        \n", "    pr_mask_onnx = ort_outs.sigmoid()[0][0].numpy()\n", "    pr_mask_onnx = resize_logits_mask_pil(pr_mask_onnx, width, height)\n", "    \n", "    pr_mask_croped = logits_croped.sigmoid()[0][0].numpy()\n", "    pr_mask_croped = resize_logits_mask_pil(pr_mask_croped, width, height)\n", "    \n", "    pr_mask_std = logits_std.sigmoid()[0][0].numpy()\n", "    pr_mask_std = resize_logits_mask_pil(pr_mask_std, width, height)\n", "    \n", "    pr_mask = logits.sigmoid()[0][0].numpy()\n", "    pr_mask = resize_logits_mask_pil(pr_mask, width, height)\n", "    \n", "    visualize(\n", "        image=pil_image, \n", "        ground_truth_mask=gt_mask,\n", "        predicted_full=pr_mask_std,\n", "        pr_mask_croped = pr_mask_croped,\n", "        predicted_ts=pr_mask,\n", "        pr_mask_onnx=pr_mask_onnx\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "1ca56bb5", "metadata": {}, "outputs": [], "source": ["type(ort_outs[0])"]}, {"cell_type": "code", "execution_count": null, "id": "86415eb0", "metadata": {}, "outputs": [], "source": ["with torch.no_grad():\n", "#         %timeit logits = new_model(x_tensor)\n", "        %timeit logits_std = model(x_tensor)\n", "#         logits_croped = croped_model(x_tensor)"]}, {"cell_type": "code", "execution_count": null, "id": "591a1564", "metadata": {}, "outputs": [], "source": ["values = np.random.randint(20, 100)\n"]}], "metadata": {"kernelspec": {"display_name": "NEMO", "language": "python", "name": "nemo"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 5}