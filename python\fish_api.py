#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fish Identification API Server
简化的鱼类识别API服务，用于钓鱼应用后端集成
"""

import json
import os
import random
import time
from datetime import datetime
from flask import Flask, request, jsonify
from werkzeug.utils import secure_filename
import hashlib

app = Flask(__name__)

# 配置
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# 确保上传目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# 加载鱼种标签
def load_fish_labels():
    """加载鱼种标签数据"""
    try:
        with open('labels.json', 'r', encoding='utf-8') as f:
            labels = json.load(f)
        return labels
    except Exception as e:
        print(f"Warning: Could not load labels.json: {e}")
        # 返回一些常见的中文鱼种作为后备
        return {
            "0": "鲫鱼 (Crucian Carp)",
            "1": "草鱼 (Grass Carp)", 
            "2": "鲤鱼 (Common Carp)",
            "3": "黑鱼 (Northern Snakehead)",
            "4": "鲈鱼 (Bass)",
            "5": "带鱼 (Largehead Hairtail)",
            "6": "青鱼 (Black Carp)",
            "7": "鳗鱼 (Eel)"
        }

FISH_LABELS = load_fish_labels()

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def mock_fish_identification(image_path):
    """
    模拟鱼类识别功能
    在实际部署中，这里应该调用真实的AI模型
    """
    # 模拟处理时间
    time.sleep(random.uniform(2, 5))
    
    # 随机选择一个鱼种
    fish_ids = list(FISH_LABELS.keys())
    predicted_id = random.choice(fish_ids)
    
    # 生成置信度
    confidence = round(random.uniform(0.75, 0.98), 3)
    
    # 获取鱼种名称
    species_name = FISH_LABELS[predicted_id]
    
    # 生成一些常见的中文鱼种名称映射
    chinese_names = {
        "Carassius auratus": "鲫鱼",
        "Ctenopharyngodon idella": "草鱼",
        "Cyprinus carpio": "鲤鱼",
        "Channa argus": "黑鱼",
        "Micropterus salmoides": "鲈鱼"
    }
    
    # 尝试提取英文名和中文名
    if "(" in species_name and ")" in species_name:
        parts = species_name.split("(")
        english_name = parts[0].strip()
        chinese_part = parts[1].replace(")", "").strip()
        chinese_name = chinese_part
    else:
        english_name = species_name
        chinese_name = chinese_names.get(species_name, "未知鱼种")
    
    return {
        "species_id": int(predicted_id),
        "species_name": chinese_name,
        "english_name": english_name,
        "scientific_name": english_name,
        "confidence": confidence,
        "processing_time": round(random.uniform(2.1, 4.8), 2)
    }

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        "status": "healthy",
        "service": "Fishial AI Fish Identification API",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    })

@app.route('/identify', methods=['POST'])
def identify_fish():
    """鱼类识别接口"""
    try:
        # 检查是否有文件上传
        if 'image' not in request.files:
            return jsonify({
                "success": False,
                "error": "No image file provided",
                "code": "NO_FILE"
            }), 400
        
        file = request.files['image']
        
        # 检查文件名
        if file.filename == '':
            return jsonify({
                "success": False,
                "error": "No file selected",
                "code": "NO_FILE_SELECTED"
            }), 400
        
        # 检查文件类型
        if not allowed_file(file.filename):
            return jsonify({
                "success": False,
                "error": "Invalid file type. Allowed types: png, jpg, jpeg, gif, webp",
                "code": "INVALID_FILE_TYPE"
            }), 400
        
        # 保存文件
        filename = secure_filename(file.filename)
        timestamp = str(int(time.time()))
        unique_filename = f"{timestamp}_{hashlib.md5(filename.encode()).hexdigest()[:8]}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(filepath)
        
        # 执行鱼类识别
        identification_result = mock_fish_identification(filepath)
        
        # 清理临时文件（可选）
        # os.remove(filepath)
        
        return jsonify({
            "success": True,
            "message": "Fish identification completed successfully",
            "data": {
                "filename": unique_filename,
                "original_filename": file.filename,
                "file_size": os.path.getsize(filepath),
                "identification": identification_result,
                "processed_at": datetime.now().isoformat()
            }
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}",
            "code": "INTERNAL_ERROR"
        }), 500

@app.route('/species', methods=['GET'])
def get_species_list():
    """获取支持的鱼种列表"""
    species_list = []
    for species_id, species_info in FISH_LABELS.items():
        if "(" in species_info and ")" in species_info:
            parts = species_info.split("(")
            english_name = parts[0].strip()
            chinese_name = parts[1].replace(")", "").strip()
        else:
            english_name = species_info
            chinese_name = "未知"
        
        species_list.append({
            "id": int(species_id),
            "name": chinese_name,
            "english_name": english_name,
            "scientific_name": english_name
        })
    
    return jsonify({
        "success": True,
        "message": "Species list retrieved successfully",
        "data": {
            "total_species": len(species_list),
            "species": species_list
        }
    })

@app.route('/remove-background', methods=['POST'])
def remove_background():
    """背景移除接口（模拟）"""
    try:
        if 'image' not in request.files:
            return jsonify({
                "success": False,
                "error": "No image file provided",
                "code": "NO_FILE"
            }), 400
        
        file = request.files['image']
        
        if not allowed_file(file.filename):
            return jsonify({
                "success": False,
                "error": "Invalid file type",
                "code": "INVALID_FILE_TYPE"
            }), 400
        
        # 模拟处理时间
        time.sleep(random.uniform(1, 3))
        
        # 保存原文件
        filename = secure_filename(file.filename)
        timestamp = str(int(time.time()))
        original_filename = f"{timestamp}_original_{filename}"
        processed_filename = f"{timestamp}_processed_{filename}"
        
        original_path = os.path.join(app.config['UPLOAD_FOLDER'], original_filename)
        file.save(original_path)
        
        # 在实际实现中，这里应该调用背景移除API或模型
        # 现在我们只是复制文件作为演示
        processed_path = os.path.join(app.config['UPLOAD_FOLDER'], processed_filename)
        
        import shutil
        shutil.copy2(original_path, processed_path)
        
        return jsonify({
            "success": True,
            "message": "Background removal completed successfully",
            "data": {
                "original_filename": original_filename,
                "processed_filename": processed_filename,
                "original_url": f"/uploads/{original_filename}",
                "processed_url": f"/uploads/{processed_filename}",
                "processing_time": round(random.uniform(1.2, 2.8), 2),
                "processed_at": datetime.now().isoformat()
            }
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}",
            "code": "INTERNAL_ERROR"
        }), 500

if __name__ == '__main__':
    print("Starting Fishial AI Fish Identification API Server...")
    print(f"Upload folder: {os.path.abspath(UPLOAD_FOLDER)}")
    print(f"Loaded {len(FISH_LABELS)} fish species")
    print("Server starting on http://localhost:5001")
    
    app.run(host='0.0.0.0', port=5001, debug=True)