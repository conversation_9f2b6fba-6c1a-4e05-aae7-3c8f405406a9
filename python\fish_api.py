#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fishial AI Fish Identification API Server
鱼类识别API服务 - 集成真实AI模型的完整版本
"""

import json
import os
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from flask import Flask, request, jsonify, send_from_directory
from werkzeug.utils import secure_filename
import hashlib

# 导入自定义模块
try:
    from model_manager import get_model_manager
    from inference_engine import get_inference_engine
    MODEL_AVAILABLE = True
except ImportError as e:
    logging.warning(f"模型模块导入失败: {e}")
    MODEL_AVAILABLE = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)

# 初始化模型管理器和推理引擎
if MODEL_AVAILABLE:
    try:
        model_manager = get_model_manager()
        inference_engine = get_inference_engine()

        # 从配置文件加载设置
        config = model_manager.get_config()
        api_config = config.get('api', {})
        data_config = config.get('data', {})
        security_config = config.get('security', {})

        UPLOAD_FOLDER = data_config.get('upload_folder', 'uploads')
        ALLOWED_EXTENSIONS = set(data_config.get('allowed_extensions', ['png', 'jpg', 'jpeg', 'gif', 'webp']))
        MAX_CONTENT_LENGTH = security_config.get('max_content_length', 16 * 1024 * 1024)

        logger.info("模型管理器和推理引擎初始化成功")
    except Exception as e:
        logger.error(f"模型初始化失败: {e}")
        MODEL_AVAILABLE = False

# 如果模型不可用，使用默认配置
if not MODEL_AVAILABLE:
    UPLOAD_FOLDER = 'uploads'
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024

# Flask配置
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# 确保必要目录存在
Path(UPLOAD_FOLDER).mkdir(parents=True, exist_ok=True)
Path('logs').mkdir(parents=True, exist_ok=True)

# 工具函数
def load_fish_labels() -> Dict[str, str]:
    """加载鱼种标签数据"""
    if MODEL_AVAILABLE:
        try:
            return model_manager.get_labels()
        except Exception as e:
            logger.warning(f"从模型管理器加载标签失败: {e}")

    # 后备方案：直接加载labels.json
    try:
        with open('labels.json', 'r', encoding='utf-8') as f:
            labels = json.load(f)
        logger.info(f"直接加载标签文件成功，共{len(labels)}个类别")
        return labels
    except Exception as e:
        logger.warning(f"标签文件加载失败: {e}")
        # 返回默认标签
        return {
            "0": "鲫鱼 (Crucian Carp)",
            "1": "草鱼 (Grass Carp)",
            "2": "鲤鱼 (Common Carp)",
            "3": "黑鱼 (Northern Snakehead)",
            "4": "鲈鱼 (Bass)",
            "5": "带鱼 (Largehead Hairtail)",
            "6": "青鱼 (Black Carp)",
            "7": "鳗鱼 (Eel)"
        }

# 全局变量
FISH_LABELS = load_fish_labels()

def allowed_file(filename: str) -> bool:
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def generate_unique_filename(original_filename: str) -> str:
    """生成唯一的文件名"""
    timestamp = str(int(time.time()))
    file_hash = hashlib.md5(original_filename.encode()).hexdigest()[:8]
    return f"{timestamp}_{file_hash}_{secure_filename(original_filename)}"

def real_fish_identification(image_path: str) -> Dict[str, Any]:
    """
    真实的鱼类识别功能
    使用训练好的AI模型进行推理
    """
    if not MODEL_AVAILABLE:
        return mock_fish_identification(image_path)

    try:
        # 使用推理引擎进行分类
        result = inference_engine.classify_fish(image_path)

        if result.get('success', False):
            return {
                "species_id": result.get('species_id', 0),
                "species_name": result.get('species_name', '未知鱼种'),
                "english_name": result.get('english_name', 'Unknown Fish'),
                "scientific_name": result.get('scientific_name', 'Unknown'),
                "confidence": result.get('confidence', 0.0),
                "processing_time": result.get('processing_time', 0.0),
                "model_used": True,
                "mock_result": result.get('mock_result', False)
            }
        else:
            logger.error(f"模型推理失败: {result.get('error', 'Unknown error')}")
            return mock_fish_identification(image_path)

    except Exception as e:
        logger.error(f"推理过程异常: {e}")
        return mock_fish_identification(image_path)

def mock_fish_identification(image_path: str) -> Dict[str, Any]:
    """
    模拟鱼类识别功能（后备方案）
    """
    import random

    # 模拟处理时间
    time.sleep(random.uniform(1, 3))

    # 随机选择一个鱼种
    fish_ids = list(FISH_LABELS.keys())
    predicted_id = random.choice(fish_ids)

    # 生成置信度
    confidence = round(random.uniform(0.75, 0.98), 3)

    # 获取鱼种名称
    species_name = FISH_LABELS[predicted_id]

    # 解析中英文名称
    if "(" in species_name and ")" in species_name:
        parts = species_name.split("(")
        english_name = parts[0].strip()
        chinese_part = parts[1].replace(")", "").strip()
        chinese_name = chinese_part
    else:
        english_name = species_name
        chinese_name = "未知鱼种"

    return {
        "species_id": int(predicted_id),
        "species_name": chinese_name,
        "english_name": english_name,
        "scientific_name": english_name,
        "confidence": confidence,
        "processing_time": round(random.uniform(1.5, 3.0), 2),
        "model_used": False,
        "mock_result": True
    }

# API路由
@app.route('/health', methods=['GET'])
def health_check():
    """健康检查和系统状态"""
    system_info = {
        "status": "healthy",
        "service": "Fishial AI Fish Identification API",
        "version": "2.0.0",
        "timestamp": datetime.now().isoformat(),
        "model_available": MODEL_AVAILABLE,
        "upload_folder": UPLOAD_FOLDER,
        "max_file_size": MAX_CONTENT_LENGTH,
        "allowed_extensions": list(ALLOWED_EXTENSIONS),
        "total_species": len(FISH_LABELS)
    }

    if MODEL_AVAILABLE:
        try:
            # 获取模型状态
            available_models = model_manager.list_available_models()
            model_validation = model_manager.validate_models()

            system_info.update({
                "models": {
                    "available": available_models,
                    "validation": model_validation
                }
            })
        except Exception as e:
            logger.error(f"获取模型状态失败: {e}")
            system_info["model_status_error"] = str(e)

    return jsonify(system_info)

@app.route('/identify', methods=['POST'])
def identify_fish():
    """鱼类识别接口 - 主要功能"""
    start_time = time.time()

    try:
        # 验证请求
        validation_error = validate_upload_request(request)
        if validation_error:
            return validation_error

        file = request.files['image']

        # 保存上传的文件
        unique_filename = generate_unique_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(filepath)

        logger.info(f"文件上传成功: {unique_filename}")

        # 执行鱼类识别
        identification_result = real_fish_identification(filepath)

        # 构建响应
        response_data = {
            "success": True,
            "message": "Fish identification completed successfully",
            "data": {
                "filename": unique_filename,
                "original_filename": file.filename,
                "file_size": os.path.getsize(filepath),
                "file_path": filepath,
                "identification": identification_result,
                "processed_at": datetime.now().isoformat(),
                "total_processing_time": round(time.time() - start_time, 3)
            }
        }

        # 记录成功日志
        logger.info(f"识别完成: {identification_result.get('species_name', 'Unknown')} "
                   f"(置信度: {identification_result.get('confidence', 0):.3f})")

        return jsonify(response_data)

    except Exception as e:
        error_msg = f"Internal server error: {str(e)}"
        logger.error(error_msg)

        return jsonify({
            "success": False,
            "error": error_msg,
            "code": "INTERNAL_ERROR",
            "processing_time": round(time.time() - start_time, 3)
        }), 500

def validate_upload_request(request) -> Optional[tuple]:
    """验证上传请求"""
    # 检查是否有文件上传
    if 'image' not in request.files:
        return jsonify({
            "success": False,
            "error": "No image file provided",
            "code": "NO_FILE"
        }), 400

    file = request.files['image']

    # 检查文件名
    if file.filename == '':
        return jsonify({
            "success": False,
            "error": "No file selected",
            "code": "NO_FILE_SELECTED"
        }), 400

    # 检查文件类型
    if not allowed_file(file.filename):
        return jsonify({
            "success": False,
            "error": f"Invalid file type. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}",
            "code": "INVALID_FILE_TYPE"
        }), 400

    return None

@app.route('/species', methods=['GET'])
def get_species_list():
    """获取支持的鱼种列表"""
    try:
        species_list = []
        for species_id, species_info in FISH_LABELS.items():
            # 解析中英文名称
            if "(" in species_info and ")" in species_info:
                parts = species_info.split("(")
                english_name = parts[0].strip()
                chinese_name = parts[1].replace(")", "").strip()
            else:
                english_name = species_info
                chinese_name = "未知"

            species_list.append({
                "id": int(species_id),
                "name": chinese_name,
                "english_name": english_name,
                "scientific_name": english_name
            })

        # 按ID排序
        species_list.sort(key=lambda x: x['id'])

        return jsonify({
            "success": True,
            "message": "Species list retrieved successfully",
            "data": {
                "total_species": len(species_list),
                "species": species_list,
                "model_available": MODEL_AVAILABLE,
                "retrieved_at": datetime.now().isoformat()
            }
        })

    except Exception as e:
        logger.error(f"获取物种列表失败: {e}")
        return jsonify({
            "success": False,
            "error": f"Failed to retrieve species list: {str(e)}",
            "code": "SPECIES_LIST_ERROR"
        }), 500

@app.route('/detect', methods=['POST'])
def detect_fish():
    """鱼类检测接口 - 检测图像中的鱼类位置"""
    start_time = time.time()

    try:
        # 验证请求
        validation_error = validate_upload_request(request)
        if validation_error:
            return validation_error

        file = request.files['image']

        # 保存文件
        unique_filename = generate_unique_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(filepath)

        logger.info(f"检测文件上传成功: {unique_filename}")

        # 执行鱼类检测
        if MODEL_AVAILABLE:
            detection_result = inference_engine.detect_fish(filepath)
        else:
            # 模拟检测结果
            detection_result = {
                "success": True,
                "detections": [
                    {
                        "bbox": [100, 100, 200, 200],
                        "confidence": 0.85,
                        "class_id": 0,
                        "class_name": "fish"
                    }
                ],
                "num_detections": 1,
                "processing_time": 2.5,
                "mock_result": True
            }

        return jsonify({
            "success": True,
            "message": "Fish detection completed successfully",
            "data": {
                "filename": unique_filename,
                "original_filename": file.filename,
                "detection": detection_result,
                "processed_at": datetime.now().isoformat(),
                "total_processing_time": round(time.time() - start_time, 3)
            }
        })

    except Exception as e:
        logger.error(f"检测失败: {e}")
        return jsonify({
            "success": False,
            "error": f"Detection failed: {str(e)}",
            "code": "DETECTION_ERROR",
            "processing_time": round(time.time() - start_time, 3)
        }), 500

@app.route('/segment', methods=['POST'])
def segment_fish():
    """鱼类分割接口 - 生成鱼类掩码"""
    start_time = time.time()

    try:
        # 验证请求
        validation_error = validate_upload_request(request)
        if validation_error:
            return validation_error

        file = request.files['image']

        # 保存文件
        unique_filename = generate_unique_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(filepath)

        logger.info(f"分割文件上传成功: {unique_filename}")

        # 执行鱼类分割
        if MODEL_AVAILABLE:
            segmentation_result = inference_engine.segment_fish(filepath)
        else:
            # 模拟分割结果
            segmentation_result = {
                "success": True,
                "mask_available": True,
                "mask_path": None,
                "processing_time": 3.2,
                "mock_result": True
            }

        return jsonify({
            "success": True,
            "message": "Fish segmentation completed successfully",
            "data": {
                "filename": unique_filename,
                "original_filename": file.filename,
                "segmentation": segmentation_result,
                "processed_at": datetime.now().isoformat(),
                "total_processing_time": round(time.time() - start_time, 3)
            }
        })

    except Exception as e:
        logger.error(f"分割失败: {e}")
        return jsonify({
            "success": False,
            "error": f"Segmentation failed: {str(e)}",
            "code": "SEGMENTATION_ERROR",
            "processing_time": round(time.time() - start_time, 3)
        }), 500

# 文件服务端点
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """提供上传文件的访问"""
    try:
        return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
    except Exception as e:
        logger.error(f"文件访问失败: {filename} - {e}")
        return jsonify({
            "success": False,
            "error": "File not found",
            "code": "FILE_NOT_FOUND"
        }), 404

@app.route('/models/status', methods=['GET'])
def get_models_status():
    """获取模型状态信息"""
    try:
        if not MODEL_AVAILABLE:
            return jsonify({
                "success": False,
                "message": "Model manager not available",
                "models_available": False
            })

        available_models = model_manager.list_available_models()
        validation_results = model_manager.validate_models()

        return jsonify({
            "success": True,
            "message": "Model status retrieved successfully",
            "data": {
                "models_available": MODEL_AVAILABLE,
                "available_models": available_models,
                "validation_results": validation_results,
                "total_species": len(FISH_LABELS),
                "checked_at": datetime.now().isoformat()
            }
        })

    except Exception as e:
        logger.error(f"获取模型状态失败: {e}")
        return jsonify({
            "success": False,
            "error": f"Failed to get model status: {str(e)}",
            "code": "MODEL_STATUS_ERROR"
        }), 500

@app.route('/api/info', methods=['GET'])
def get_api_info():
    """获取API信息和使用说明"""
    api_info = {
        "service": "Fishial AI Fish Identification API",
        "version": "2.0.0",
        "description": "基于深度学习的鱼类识别、检测和分割API服务",
        "endpoints": {
            "GET /health": "健康检查和系统状态",
            "GET /api/info": "API信息和使用说明",
            "GET /species": "获取支持的鱼种列表",
            "GET /models/status": "获取模型状态信息",
            "POST /identify": "鱼类识别 - 上传图像识别鱼种",
            "POST /detect": "鱼类检测 - 检测图像中鱼类的位置",
            "POST /segment": "鱼类分割 - 生成鱼类掩码",
            "GET /uploads/<filename>": "访问上传的文件"
        },
        "supported_formats": list(ALLOWED_EXTENSIONS),
        "max_file_size": f"{MAX_CONTENT_LENGTH / (1024*1024):.1f}MB",
        "total_species": len(FISH_LABELS),
        "model_available": MODEL_AVAILABLE,
        "documentation": {
            "github": "https://github.com/fishial/fish-identification",
            "website": "https://www.fishial.ai"
        }
    }

    return jsonify(api_info)

# 错误处理
@app.errorhandler(413)
def too_large(e):
    """文件过大错误处理"""
    return jsonify({
        "success": False,
        "error": f"File too large. Maximum size: {MAX_CONTENT_LENGTH / (1024*1024):.1f}MB",
        "code": "FILE_TOO_LARGE"
    }), 413

@app.errorhandler(404)
def not_found(e):
    """404错误处理"""
    return jsonify({
        "success": False,
        "error": "Endpoint not found",
        "code": "NOT_FOUND",
        "available_endpoints": [
            "/health", "/api/info", "/species", "/models/status",
            "/identify", "/detect", "/segment"
        ]
    }), 404

@app.errorhandler(500)
def internal_error(e):
    """500错误处理"""
    logger.error(f"Internal server error: {e}")
    return jsonify({
        "success": False,
        "error": "Internal server error",
        "code": "INTERNAL_ERROR"
    }), 500

if __name__ == '__main__':
    print("=" * 60)
    print("🐟 Fishial AI Fish Identification API Server")
    print("=" * 60)
    print(f"📁 Upload folder: {os.path.abspath(UPLOAD_FOLDER)}")
    print(f"🏷️  Loaded {len(FISH_LABELS)} fish species")
    print(f"🤖 Model available: {'✓' if MODEL_AVAILABLE else '✗'}")
    print(f"📊 Max file size: {MAX_CONTENT_LENGTH / (1024*1024):.1f}MB")
    print(f"📝 Supported formats: {', '.join(ALLOWED_EXTENSIONS)}")
    print("=" * 60)

    # 获取配置
    if MODEL_AVAILABLE:
        api_config = model_manager.get_config('api')
        host = api_config.get('host', '0.0.0.0')
        port = api_config.get('port', 5001)
        debug = api_config.get('debug', True)
    else:
        host = '0.0.0.0'
        port = 5001
        debug = True

    print(f"🚀 Server starting on http://{host}:{port}")
    print(f"📖 API documentation: http://{host}:{port}/api/info")
    print("=" * 60)

    try:
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Server failed to start: {e}")
        logger.error(f"Server startup failed: {e}")