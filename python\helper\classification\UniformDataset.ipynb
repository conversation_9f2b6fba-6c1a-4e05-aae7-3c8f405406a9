{"cells": [{"cell_type": "code", "execution_count": null, "id": "2cd0baf7", "metadata": {}, "outputs": [], "source": ["import json\n", "import random"]}, {"cell_type": "code", "execution_count": null, "id": "3ed5e9b8", "metadata": {}, "outputs": [], "source": ["def remove_indices(data, indieces):\n", "    for del_idx in indieces:\n", "        for row in data:\n", "            del data[row][del_idx]\n", "\n", "def disturb_dataset(data, img_per_class):\n", "    \n", "    for label_encoded in list(set(data['label_encoded'])):\n", "        indices = [i for i, x in enumerate(data['label_encoded']) if x == label_encoded]\n", "        if len(indices) > img_per_class:\n", "            del_indices = random.sample(indices, len(indices) - img_per_class)\n", "            del_indices.sort(reverse= True)\n", "            remove_indices(data, del_indices)"]}, {"cell_type": "code", "execution_count": null, "id": "faedb60a", "metadata": {}, "outputs": [], "source": ["max_img_per_class_train = 56\n", "max_img_per_class_test = 25\n", "\n", "with open('dataset/data_train.json') as json_file:\n", "    data_train = json.load(json_file)\n", "    \n", "with open('dataset/data_test.json') as json_file:\n", "    data_test = json.load(json_file)"]}, {"cell_type": "code", "execution_count": null, "id": "b8679668", "metadata": {}, "outputs": [], "source": ["print(\"before: {}\".format(len(data_train['label'])))\n", "disturb_dataset(data_train, max_img_per_class_train)\n", "print(\"after: {}\".format(len(data_train['label'])))\n", "\n", "print(\"before: {}\".format(len(data_test['label'])))\n", "disturb_dataset(data_test, max_img_per_class_test)\n", "print(\"after: {}\".format(len(data_test['label'])))"]}, {"cell_type": "code", "execution_count": null, "id": "d1dbcde2", "metadata": {}, "outputs": [], "source": ["test = {k:0 for k in list(set(data_train['label_encoded']))}\n", "for i in data_train['label_encoded']:\n", "    test[i] += 1\n", "{k: v for k, v in sorted(test.items(), key=lambda item: item[1])}"]}, {"cell_type": "code", "execution_count": null, "id": "e5330be9", "metadata": {}, "outputs": [], "source": ["path_to_images_folder = r'/home/<USER>/Fishial/dataset/fishial_collection/data'\n", "filenames = next(walk(path_to_images_folder), (None, None, []))[2]"]}, {"cell_type": "code", "execution_count": null, "id": "23b07334", "metadata": {}, "outputs": [], "source": ["import cv2\n", "from os import walk\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "2060b8d7", "metadata": {}, "outputs": [], "source": ["data = cv2.imread(r'/home/<USER>/Fishial/dataset/fishial_collection/data/00401597.jpg;')"]}, {"cell_type": "code", "execution_count": null, "id": "d49d58b3", "metadata": {"scrolled": true}, "outputs": [], "source": ["plt.imshow(data)"]}, {"cell_type": "code", "execution_count": null, "id": "e7deded9", "metadata": {}, "outputs": [], "source": ["import os\n", "from os import listdir\n", "from os.path import isfile, join\n", "path_to_empty_ann = r'/home/<USER>/Fishial/dataset/fishial_collection/data'\n", "onlyfiles = [f for f in listdir(path_to_empty_ann) if isfile(join(path_to_empty_ann, f))]"]}, {"cell_type": "code", "execution_count": null, "id": "99f3491a", "metadata": {}, "outputs": [], "source": ["'00401597.jpg;' in onlyfiles"]}, {"cell_type": "code", "execution_count": null, "id": "bfaf25ac", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "#Change path specificly to your directories\n", "sys.path.insert(1, '/home/<USER>/Fishial/Object-Detection-Model')\n", "from module.segmentation_package.src.utils import remove_tmp_files\n", "from module.segmentation_package.src.utils import read_json  \n", "from module.segmentation_package.src.utils import save_json  \n", "from module.segmentation_package.src.utils import get_mask\n", "import numpy as np\n", "import pyclipper"]}, {"cell_type": "code", "execution_count": null, "id": "bd2b6d4e", "metadata": {}, "outputs": [], "source": ["folder_path = r'/home/<USER>/Fishial/dataset/export'\n", "json_path = r'03_export_Verified_ALL.json.json'\n", "\n", "data = read_json(os.path.join(folder_path, json_path))\n", "def get_image_by_id(data, id):\n", "    for img_inst in data['images']:\n", "        if img_inst['id'] == id:\n", "            return img_inst\n", "def fix_poly(poly, size):\n", "    return "]}, {"cell_type": "code", "execution_count": null, "id": "0cba7405", "metadata": {"scrolled": false}, "outputs": [], "source": ["cnt = 0\n", "for idx, ann in enumerate(data['annotations']):\n", "    \n", "    img = cv2.imread(r'/home/<USER>/Fishial/dataset/fishial_collection/data/' + get_image_by_id(data, ann['image_id'])['file_name'] )\n", "    try:\n", "        poly_raw = ann['segmentation'][0]\n", "    #         print(ann)\n", "        height, width = img.shape[:2]\n", "        polyline_main = [[ max(0, min(width, int(poly_raw[point_id * 2]) ) ), \n", "                          max(0, min(height, int(poly_raw[point_id * 2 + 1])))\n", "                         ] for point_id in\n", "                         range(int(len(poly_raw) / 2))]\n", "        polygon = Polygon(polyline_main)\n", "        if not polygon.is_valid:\n", "            cnt += 1\n", "            print(f\"cnt: {cnt} idx: {idx}\", end = '\\r')\n", "            data['annotations'][idx].update({'is_valid': False})\n", "            mask = get_mask(img, np.array(polyline_main))\n", "        else:\n", "            data['annotations'][idx].update({'is_valid': True})\n", "    except:\n", "        data['annotations'][idx].update({'is_valid': False})\n", "        "]}, {"cell_type": "code", "execution_count": null, "id": "85e87caa", "metadata": {}, "outputs": [], "source": ["from shapely.geometry import Point\n", "from shapely.geometry.polygon import Polygon\n", "\n", "polygon = Polygon(polyline_main)\n", "print(polygon.is_valid)"]}, {"cell_type": "code", "execution_count": null, "id": "3ca784e0", "metadata": {}, "outputs": [], "source": ["save_json(data, os.path.join(folder_path, \"fixed_all_json.json\"))"]}, {"cell_type": "code", "execution_count": null, "id": "8111c366", "metadata": {}, "outputs": [], "source": ["def is_valid(ann, img_folder = r'/home/<USER>/Fishial/dataset/fishial_collection/data' ):\n", "    img = cv2.imread(os.path.join(img_folder, get_image_by_id(data, ann['image_id'])['file_name'] ))\n", "    poly_raw = ann['segmentation'][0]\n", "    height, width = img.shape[:2]\n", "    \n", "    polyline_main = [[ max(0, min(width, int(poly_raw[point_id * 2]))), \n", "                      max(0, min(height, int(poly_raw[point_id * 2 + 1])))\n", "                     ] for point_id in\n", "                     range(int(len(poly_raw) / 2))]\n", "    \n", "    polygon = Polygon(polyline_main)\n", "    return polygon.is_valid"]}, {"cell_type": "code", "execution_count": 1, "id": "5783b564", "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "# Change path specificly to your directories\n", "sys.path.insert(1, '/home/<USER>/Fishial/Object-Detection-Model')\n", "from module.classification_package.src.dataset import FishialDataset, FishialDatasetOnlineCuting\n", "from module.classification_package.src.dataset import BalancedBatchSampler\n", "from torchvision import transforms\n", "from PIL import Image"]}, {"cell_type": "code", "execution_count": 2, "id": "48916af7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/detectron2_env/lib/python3.6/site-packages/torchvision/transforms/transforms.py:288: UserWarning: Argument interpolation should be of type InterpolationMode instead of int. Please, use InterpolationMode enum.\n", "  \"Argument interpolation should be of type InterpolationMode instead of int. \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r", "isn't valid\r"]}], "source": ["ds_train = FishialDatasetOnlineCuting(\n", "        path_to_images_folder=r'/home/<USER>/Fishial/dataset/fishial_collection/data',\n", "        path_to_COCO_file=r'/home/<USER>/Fishial/dataset/export/fixed_all_json.json',\n", "        dataset_type='train',\n", "        train_state=True,\n", "        transform=transforms.Compose([transforms.Resize((224, 224), Image.BILINEAR),\n", "                                      transforms.TrivialAugmentWide(),\n", "                                      transforms.RandomHorizontalFlip(),\n", "                                      transforms.RandomVerticalFlip(),\n", "                                      transforms.To<PERSON><PERSON><PERSON>(),\n", "                                      transforms.RandomErasing(p=0.358, scale=(0.05, 0.4), ratio=(0.05, 6.1),\n", "                                                               value=0, inplace=False),\n", "                                      transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])\n", "    )"]}, {"cell_type": "code", "execution_count": 3, "id": "96de03d0", "metadata": {}, "outputs": [], "source": ["balanced_batch_sampler_ds_train = BalancedBatchSampler(ds_train, 5, 15)"]}, {"cell_type": "code", "execution_count": 4, "id": "983ef33a", "metadata": {}, "outputs": [], "source": ["from torch import nn\n", "from torch.utils.data import DataLoader"]}, {"cell_type": "code", "execution_count": 13, "id": "3a92ee58", "metadata": {}, "outputs": [], "source": ["\n", "data_loader_train = DataLoader(ds_train, batch_sampler=balanced_batch_sampler_ds_train,\n", "                               num_workers=2,\n", "                               pin_memory=True)  # Construct your Dataloader here"]}, {"cell_type": "code", "execution_count": 15, "id": "78dcee4a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n", "len batch: 2\n", "len images: 75\n", "len labels: 75\n", "++++++++++\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-15-fc1ef1426530>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0;32mfor\u001b[0m \u001b[0mbatch_idx\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mbatch\u001b[0m \u001b[0;32min\u001b[0m \u001b[0menumerate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdata_loader_train\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m     \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34mf\"len batch: {len(batch)}\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m     \u001b[0mbatch\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mtuple\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mt\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mto\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'cuda'\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mt\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mbatch\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m     \u001b[0mimages\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mlabels\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mbatch\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m     \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34mf\"len images: {len(images)}\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/miniconda3/envs/detectron2_env/lib/python3.6/site-packages/torch/utils/data/dataloader.py\u001b[0m in \u001b[0;36m__next__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    519\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_sampler_iter\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    520\u001b[0m                 \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_reset\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 521\u001b[0;31m             \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_next_data\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    522\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_num_yielded\u001b[0m \u001b[0;34m+=\u001b[0m \u001b[0;36m1\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    523\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_dataset_kind\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0m_DatasetKind\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mIterable\u001b[0m \u001b[0;32mand\u001b[0m\u001b[0;31m \u001b[0m\u001b[0;31m\\\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/miniconda3/envs/detectron2_env/lib/python3.6/site-packages/torch/utils/data/dataloader.py\u001b[0m in \u001b[0;36m_next_data\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1184\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1185\u001b[0m             \u001b[0;32massert\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_shutdown\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_tasks_outstanding\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0;36m0\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1186\u001b[0;31m             \u001b[0midx\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_get_data\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1187\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_tasks_outstanding\u001b[0m \u001b[0;34m-=\u001b[0m \u001b[0;36m1\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1188\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_dataset_kind\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0m_DatasetKind\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mIterable\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/miniconda3/envs/detectron2_env/lib/python3.6/site-packages/torch/utils/data/dataloader.py\u001b[0m in \u001b[0;36m_get_data\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1140\u001b[0m         \u001b[0;32melif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_pin_memory\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1141\u001b[0m             \u001b[0;32mwhile\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_pin_memory_thread\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mis_alive\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1142\u001b[0;31m                 \u001b[0msuccess\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_try_get_data\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1143\u001b[0m                 \u001b[0;32mif\u001b[0m \u001b[0msuccess\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1144\u001b[0m                     \u001b[0;32mreturn\u001b[0m \u001b[0mdata\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/miniconda3/envs/detectron2_env/lib/python3.6/site-packages/torch/utils/data/dataloader.py\u001b[0m in \u001b[0;36m_try_get_data\u001b[0;34m(self, timeout)\u001b[0m\n\u001b[1;32m    988\u001b[0m         \u001b[0;31m#   (bool: whether successfully get data, any: data if successful else None)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    989\u001b[0m         \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 990\u001b[0;31m             \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_data_queue\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mtimeout\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mtimeout\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    991\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdata\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    992\u001b[0m         \u001b[0;32mexcept\u001b[0m \u001b[0mException\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/miniconda3/envs/detectron2_env/lib/python3.6/queue.py\u001b[0m in \u001b[0;36mget\u001b[0;34m(self, block, timeout)\u001b[0m\n\u001b[1;32m    171\u001b[0m                     \u001b[0;32mif\u001b[0m \u001b[0mremaining\u001b[0m \u001b[0;34m<=\u001b[0m \u001b[0;36m0.0\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    172\u001b[0m                         \u001b[0;32mraise\u001b[0m \u001b[0mEmpty\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 173\u001b[0;31m                     \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnot_empty\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mwait\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mremaining\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    174\u001b[0m             \u001b[0mitem\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_get\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    175\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnot_full\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnotify\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/miniconda3/envs/detectron2_env/lib/python3.6/threading.py\u001b[0m in \u001b[0;36mwait\u001b[0;34m(self, timeout)\u001b[0m\n\u001b[1;32m    297\u001b[0m             \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    298\u001b[0m                 \u001b[0;32mif\u001b[0m \u001b[0mtimeout\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0;36m0\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 299\u001b[0;31m                     \u001b[0mgotit\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mwaiter\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0macquire\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtimeout\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    300\u001b[0m                 \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    301\u001b[0m                     \u001b[0mgotit\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mwaiter\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0macquire\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;32mFalse\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["for batch_idx, batch in enumerate(data_loader_train):\n", "    print(f\"len batch: {len(batch)}\")\n", "    batch = tuple(t.to('cuda') for t in batch)\n", "    images, labels = batch\n", "    print(f\"len images: {len(images)}\")\n", "    print(f\"len labels: {len(labels)}\")\n", "    print(10 * \"+\")"]}, {"cell_type": "code", "execution_count": null, "id": "50281bf2", "metadata": {}, "outputs": [], "source": ["batch[0]"]}, {"cell_type": "code", "execution_count": 8, "id": "5d25fec8", "metadata": {}, "outputs": [], "source": ["classes = next(iter(data_loader_train))   "]}, {"cell_type": "code", "execution_count": 12, "id": "ac46312e", "metadata": {"scrolled": false}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([3, 224, 224])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["classes[0][0].shape"]}, {"cell_type": "code", "execution_count": null, "id": "6f272023", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "detectron2_env", "language": "python", "name": "detectron2_env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.13"}}, "nbformat": 4, "nbformat_minor": 5}