# Fishial AI Configuration File
# 鱼类识别系统配置文件

# 模型配置
models:
  classification:
    # 分类模型路径
    model_path: "models/classification/"
    # 支持的模型文件
    model_files:
      - "classification_rectangle_v9-2.zip"  # ConvNeXt Tiny (640 classes, embed 256)
      - "classification_rectangle_v9-3.zip"  # beitv2_base_patch16_224 (640 classes, embed 512)
    # 默认使用的模型
    default_model: "classification_rectangle_v9-3.zip"
    # 输入图像尺寸
    input_size: [224, 224]
    # 标签文件路径
    labels_path: "labels.json"
    # 置信度阈值
    confidence_threshold: 0.5
    # 嵌入维度
    embedding_dim: 512
    
  detection:
    # 检测模型路径
    model_path: "models/detection/"
    # 模型文件
    model_file: "detector_v10_m5.zip"  # YOLOv12 Medium
    # 输入图像尺寸
    input_size: [640, 640]
    # 置信度阈值
    confidence_threshold: 0.5
    # NMS阈值
    nms_threshold: 0.4
    
  segmentation:
    # 分割模型路径
    model_path: "models/segmentation/"
    # 模型文件
    model_file: "segmentator_fpn_res18_416_1.zip"  # FPN w/ ResNet18
    # 输入图像尺寸
    input_size: [416, 416]
    # 置信度阈值
    confidence_threshold: 0.5

# 数据配置
data:
  # 上传文件夹
  upload_folder: "uploads"
  # 最大文件大小 (16MB)
  max_file_size: 16777216
  # 允许的文件扩展名
  allowed_extensions: ["png", "jpg", "jpeg", "gif", "webp", "bmp", "tiff"]
  # 图像预处理参数
  preprocessing:
    # 是否调整图像大小
    resize: true
    # 是否标准化
    normalize: true
    # 标准化参数 (ImageNet)
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]

# API配置
api:
  # 服务器主机
  host: "0.0.0.0"
  # 服务器端口
  port: 5001
  # 调试模式
  debug: true
  # 跨域设置
  cors_enabled: true
  # API版本
  version: "1.0.0"
  # 服务名称
  service_name: "Fishial AI Fish Identification API"

# 日志配置
logging:
  # 日志级别
  level: "INFO"
  # 日志文件路径
  file_path: "logs/fishial_api.log"
  # 日志格式
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  # 是否输出到控制台
  console_output: true

# 性能配置
performance:
  # 是否使用GPU
  use_gpu: false
  # GPU设备ID
  gpu_device: 0
  # 批处理大小
  batch_size: 1
  # 线程数
  num_workers: 4
  # 模型缓存
  model_cache: true
  # 预热模型
  model_warmup: true

# 安全配置
security:
  # 最大请求大小
  max_content_length: 16777216
  # 请求超时时间（秒）
  request_timeout: 30
  # 是否启用请求限制
  rate_limiting: false
  # 每分钟最大请求数
  max_requests_per_minute: 60

# 缓存配置
cache:
  # 是否启用缓存
  enabled: false
  # 缓存类型 (memory, redis, file)
  type: "memory"
  # 缓存过期时间（秒）
  expire_time: 3600
  # 最大缓存条目数
  max_entries: 1000
