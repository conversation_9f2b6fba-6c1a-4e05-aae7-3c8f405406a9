# 🐟 Fishial AI 独立环境使用指南

## 环境信息
- 虚拟环境路径: E:\ReactProject\fishing-app\python\fishial_env
- Python可执行文件: E:\ReactProject\fishing-app\python\fishial_env\Scripts\python.exe
- 系统平台: Windows

## 激活环境

### Windows:
```cmd
E:\ReactProject\fishing-app\python\fishial_env\Scripts\activate.bat
```

### Linux/Mac:
```bash
source E:\ReactProject\fishing-app\python\fishial_env\Scripts\activate.bat
```

## 启动服务

### 方式1: 使用启动脚本
- Windows: 双击 `start_fishial.bat`
- Linux/Mac: 运行 `./start_fishial.sh`

### 方式2: 手动启动
```bash
# 激活环境
call E:\ReactProject\fishing-app\python\fishial_env\Scripts\activate.bat

# 启动服务
python fish_api.py
```

## API访问
- 服务地址: http://localhost:5001
- API文档: http://localhost:5001/api/info
- 健康检查: http://localhost:5001/health

## 测试API
```bash
# 健康检查
curl http://localhost:5001/health

# 鱼类识别 (需要图片文件)
curl -X POST -F "image=@your_fish_image.jpg" http://localhost:5001/identify
```

## 故障排除
1. 如果启动失败，检查虚拟环境是否正确激活
2. 如果模型加载失败，检查models目录下的文件是否完整
3. 如果依赖包缺失，重新运行 `python setup_environment.py`

## 环境管理
- 重新安装环境: `python setup_environment.py`
- 删除环境: 删除 `fishial_env` 文件夹
