{"cells": [{"cell_type": "code", "execution_count": 1, "id": "695ddbfe", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import pandas as pd  \n", "\n", "sys.path.insert(1, '/home/<USER>/Fishial/Object-Detection-Model')\n", "from module.classification_package.src.utils import read_json, save_json"]}, {"cell_type": "code", "execution_count": 2, "id": "7ec17e71", "metadata": {}, "outputs": [], "source": ["wrong_classified_path = r'/home/<USER>/Fishial/output/classification/resnet_18_triplet_08_09_2023_v06_under_train_cross/wrong_classified.json'\n", "data = read_json(wrong_classified_path)\n", "\n", "idx = read_json('/home/<USER>/Fishial/output/classification/resnet_18_triplet_08_09_2023_v06_under_train_cross/idx.json')"]}, {"cell_type": "code", "execution_count": 3, "id": "9e164d07", "metadata": {}, "outputs": [], "source": ["#sort by closest distancee, which mean the most possible misstake in fishial export. \n", "data = sorted(data, key=lambda x: x[6], reverse=False)"]}, {"cell_type": "code", "execution_count": 4, "id": "4654d097", "metadata": {}, "outputs": [], "source": ["#rewrite to excel\n", "df = pd.DataFrame(data, columns = ['label','image_id', 'annotation_id', 'drawn_fish_id','fc_output','embedding_output', 'distance'])\n", "df.to_excel('misclassified_list.xlsx', sheet_name='Sheet1', index=False)"]}, {"cell_type": "code", "execution_count": 5, "id": "7a5b6842", "metadata": {}, "outputs": [], "source": ["dict_process = {\n", "    idx['categories'][i]['name']: {\n", "        'emb' : [],\n", "        'fc'  : [],\n", "        'dist': [],\n", "        'all' : []\n", "    } for i in idx['categories'].keys()\n", "}\n", "\n", "for i in data:\n", "    dict_process[i[0]]['emb'].append(i[5])\n", "    dict_process[i[0]]['fc'].append(i[4])\n", "    dict_process[i[0]]['dist'].append(i[6])"]}, {"cell_type": "code", "execution_count": 6, "id": "f86d409b", "metadata": {}, "outputs": [], "source": ["for i in idx['list_of_ids']:\n", "    label_true = idx['categories'][str(i[0])]['name']\n", "    dict_process[label_true]['all'].append(1)"]}, {"cell_type": "code", "execution_count": 7, "id": "60cf67e9", "metadata": {}, "outputs": [], "source": ["accuracy_list = []\n", "for label_name in dict_process:\n", "    accuracy_list.append([label_name, (1  - len(dict_process[label_name]['emb'])/len(dict_process[label_name]['all'])) * 100 ])"]}, {"cell_type": "code", "execution_count": 8, "id": "245ee708", "metadata": {}, "outputs": [], "source": ["accuracy_list = sorted(accuracy_list, key=lambda x: x[1], reverse=False) "]}, {"cell_type": "code", "execution_count": 9, "id": "c930f6c3", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["[['<PERSON><PERSON><PERSON> notius', 83.33333333333334],\n", " ['Carcharhinus plumbeus', 86.66666666666667],\n", " ['Hypanus americanus', 87.85871964679912],\n", " ['<PERSON><PERSON><PERSON><PERSON> fuliginosus', 89.7196261682243],\n", " ['S<PERSON><PERSON> acanthi<PERSON>', 90.19607843137256],\n", " ['Acanthopagrus butcheri', 90.29850746268657],\n", " ['Paralichthys dentatus', 90.52631578947368],\n", " ['<PERSON><PERSON><PERSON> an<PERSON>', 91.42857142857143],\n", " ['Barbus barbus', 92.10526315789474],\n", " ['Carcharhinus obscurus', 92.1875],\n", " ['Rhizoprionodon terraenovae', 92.5531914893617],\n", " ['<PERSON><PERSON><PERSON><PERSON>pha<PERSON> indicus', 92.78350515463917],\n", " ['<PERSON>ei<PERSON> catus', 92.89099526066352],\n", " ['Thymallus arcticus', 93.04347826086956],\n", " ['<PERSON><PERSON><PERSON><PERSON> harengus', 93.10344827586206],\n", " ['<PERSON>eiurus nebulosus', 93.14159292035397],\n", " ['Epinephe<PERSON> lanceolatus', 93.16239316239316],\n", " ['<PERSON><PERSON> chryso<PERSON>', 93.1740614334471],\n", " ['<PERSON>pt<PERSON> treculii', 93.33333333333333],\n", " ['<PERSON><PERSON> belone', 93.33333333333333],\n", " ['<PERSON><PERSON><PERSON> rostrata', 93.37016574585635],\n", " ['Micropt<PERSON> salmoides', 93.44894026974951],\n", " ['<PERSON>ei<PERSON> melas', 93.46846846846847],\n", " ['<PERSON><PERSON><PERSON> ferrugineus', 93.58974358974359],\n", " ['Carcha<PERSON><PERSON>us limbatus', 93.92523364485982],\n", " ['Morone chrysops X Morone saxatilis', 94.34782608695652],\n", " ['<PERSON><PERSON><PERSON> glani<PERSON>', 94.35483870967742],\n", " ['Oncorhynch<PERSON> gorb<PERSON>', 94.4055944055944],\n", " ['<PERSON>cha<PERSON><PERSON><PERSON> leuca<PERSON>', 94.53125],\n", " ['Esox americanus americanus', 94.57364341085271],\n", " ['Epinephelus malabaricus', 94.68085106382979],\n", " ['<PERSON>pterus floridanus', 94.87179487179486],\n", " ['<PERSON><PERSON>us auratus', 95.0207468879668],\n", " ['<PERSON><PERSON>', 95.1219512195122],\n", " ['<PERSON><PERSON><PERSON><PERSON> rubrofuscus', 95.13888888888889],\n", " ['On<PERSON><PERSON>nchus t<PERSON>', 95.13888888888889],\n", " ['<PERSON><PERSON><PERSON><PERSON> oculatus', 95.1923076923077],\n", " ['Oncorhynch<PERSON> mykiss', 95.24714828897338],\n", " ['<PERSON><PERSON><PERSON> rivoliana', 95.30516431924883],\n", " ['Salmo salar', 95.3125],\n", " ['Paralichthys californicus', 95.37037037037037],\n", " ['<PERSON><PERSON><PERSON> clu<PERSON>is', 95.40229885057472],\n", " ['Plat<PERSON>thy<PERSON> flesus', 95.45454545454545],\n", " ['Oncorhynchus kisutch', 95.51020408163265],\n", " ['Men<PERSON><PERSON><PERSON><PERSON> saxatilis', 95.6896551724138],\n", " ['Triaenodon obesus', 95.74468085106383],\n", " ['Sander canadensis', 95.8041958041958],\n", " ['Paralichthys lethostigma', 95.83333333333334],\n", " ['<PERSON><PERSON><PERSON> confluentus', 95.90163934426229],\n", " ['<PERSON>ei<PERSON> natalis', 96.0088691796009],\n", " ['Alosa sapidissima', 96.02649006622516],\n", " ['<PERSON><PERSON><PERSON><PERSON><PERSON> olivaris', 96.0431654676259],\n", " ['Strongylura marina', 96.05263157894737],\n", " ['Neogobius melanostomus', 96.07843137254902],\n", " ['Ictalurus punctatus', 96.22222222222221],\n", " ['<PERSON><PERSON><PERSON><PERSON> aspius', 96.22641509433963],\n", " ['Makaira nigricans', 96.2406015037594],\n", " ['Micropt<PERSON> coosae', 96.26168224299066],\n", " ['Mustelus canis', 96.26865671641791],\n", " ['Acipenser fulvescens', 96.26865671641791],\n", " ['Lepo<PERSON> macrochirus', 96.27192982456141],\n", " ['Carpiodes carpio', 96.3302752293578],\n", " ['Lutjanus cyanopterus', 96.36363636363636],\n", " ['Epinephe<PERSON> marginatus', 96.52173913043478],\n", " ['Cynos<PERSON> regalis', 96.61016949152543],\n", " ['<PERSON><PERSON><PERSON> japonicus', 96.61016949152543],\n", " ['Ictal<PERSON> furcatus', 96.61538461538461],\n", " ['<PERSON><PERSON><PERSON><PERSON> argent<PERSON>', 96.63865546218487],\n", " ['A<PERSON><PERSON> truttacea', 96.66666666666667],\n", " ['Macquaria novemaculeata', 96.69421487603306],\n", " ['Carcharodon carcharias', 96.73202614379085],\n", " ['Scomb<PERSON>mor<PERSON> commerson', 96.7741935483871],\n", " ['<PERSON><PERSON><PERSON> punctatus', 96.81528662420382],\n", " ['<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> marmoratus', 96.96969696969697],\n", " ['Pseudopleuronectes americanus', 96.99248120300751],\n", " ['Oncorhynchus nerka', 97.01492537313433],\n", " ['<PERSON><PERSON><PERSON><PERSON> barracuda', 97.0276008492569],\n", " ['Oncorhynchus clar<PERSON>i', 97.0414201183432],\n", " ['<PERSON><PERSON><PERSON>', 97.05882352941177],\n", " ['<PERSON>ia calva', 97.07207207207207],\n", " ['Istiophorus albicans', 97.10144927536231],\n", " ['<PERSON><PERSON><PERSON><PERSON>ei', 97.10144927536231],\n", " ['Men<PERSON><PERSON><PERSON><PERSON> americanus', 97.10144927536231],\n", " ['Thymallus thymallus', 97.24770642201834],\n", " ['Semotilus atromaculatus', 97.27272727272728],\n", " ['Prosopium william<PERSON>i', 97.36842105263158],\n", " ['<PERSON>na striata', 97.40259740259741],\n", " ['Esox masquin<PERSON>', 97.41697416974169],\n", " ['<PERSON><PERSON> ma<PERSON>', 97.43589743589743],\n", " ['<PERSON><PERSON><PERSON><PERSON> platyrhincus', 97.44680851063829],\n", " ['Labrus bergylta', 97.45762711864407],\n", " ['Pachymetop<PERSON> blochii', 97.45762711864407],\n", " ['<PERSON><PERSON><PERSON> morhua', 97.47899159663865],\n", " ['<PERSON><PERSON><PERSON> gulosus', 97.53914988814317],\n", " ['Ginglymostoma cirratum', 97.54098360655738],\n", " ['<PERSON><PERSON><PERSON> past<PERSON>', 97.54098360655738],\n", " ['Pty<PERSON>che<PERSON><PERSON> oregonensis', 97.54098360655738],\n", " ['At<PERSON>tosteus spatula', 97.61904761904762],\n", " ['Ophiodon elongatus', 97.61904761904762],\n", " ['Thunnus atlanticus', 97.67441860465115],\n", " ['<PERSON><PERSON><PERSON><PERSON> merlangus', 97.70992366412213],\n", " ['<PERSON><PERSON><PERSON><PERSON> annularis', 97.72209567198178],\n", " ['<PERSON><PERSON><PERSON><PERSON> all<PERSON>', 97.72727272727273],\n", " ['<PERSON><PERSON><PERSON><PERSON> jocu', 97.74436090225565],\n", " ['Lota lota', 97.74436090225565],\n", " ['Rutilus rutilus', 97.78270509977827],\n", " ['Oreoch<PERSON>is aureus', 97.78481012658227],\n", " ['<PERSON><PERSON><PERSON><PERSON> bohar', 97.79411764705883],\n", " ['Carcharias taurus', 97.9020979020979],\n", " ['<PERSON><PERSON><PERSON> m<PERSON>', 97.91666666666666],\n", " ['Oncorhynchus keta', 97.94520547945206],\n", " ['Hiodon alosoides', 97.95918367346938],\n", " ['Caranx hippos', 97.96839729119638],\n", " ['Esox niger', 97.96839729119638],\n", " ['Ariopsis felis', 98.0],\n", " ['Caranx ruber', 98.00443458980045],\n", " ['<PERSON><PERSON><PERSON> gari<PERSON>', 98.07692307692307],\n", " ['<PERSON><PERSON><PERSON><PERSON><PERSON> macrocephalus', 98.11320754716981],\n", " ['Salmo trutta', 98.14814814814815],\n", " ['Opsanus tau', 98.14814814814815],\n", " ['Paralabrax maculatofasciatus', 98.14814814814815],\n", " ['Oligoplites saurus', 98.1651376146789],\n", " ['<PERSON><PERSON><PERSON><PERSON> carpio carpio', 98.1651376146789],\n", " ['Micropt<PERSON> do<PERSON>', 98.25918762088975],\n", " ['Sparisoma viride', 98.27586206896551],\n", " ['Moxostoma macrolepidotum', 98.27586206896551],\n", " ['<PERSON><PERSON><PERSON> crocodilus', 98.29059829059828],\n", " ['<PERSON><PERSON><PERSON> b<PERSON>', 98.29059829059828],\n", " ['Moxostoma anisurum', 98.29059829059828],\n", " ['Scaph<PERSON>hynch<PERSON> plator<PERSON>hus', 98.30508474576271],\n", " ['<PERSON><PERSON><PERSON><PERSON> striatus', 98.31932773109243],\n", " ['Myliobatis aquila', 98.33333333333333],\n", " ['Epinephelus merra', 98.34710743801654],\n", " ['<PERSON><PERSON><PERSON>', 98.38056680161942],\n", " ['<PERSON><PERSON><PERSON> du<PERSON>', 98.38337182448036],\n", " ['Epinephelus coio<PERSON>', 98.4],\n", " ['<PERSON><PERSON><PERSON><PERSON><PERSON> canicula', 98.4],\n", " ['<PERSON><PERSON><PERSON><PERSON> synagris', 98.42271293375394],\n", " ['<PERSON><PERSON><PERSON><PERSON> car<PERSON>', 98.42342342342343],\n", " ['Rhomboplites aurorubens', 98.4251968503937],\n", " ['Thunnus albacares', 98.46153846153847],\n", " ['Esox lucius', 98.47161572052401],\n", " ['<PERSON> clavata', 98.50746268656717],\n", " ['Caranx melampygus', 98.51851851851852],\n", " ['Arripis trutta', 98.52941176470588],\n", " ['<PERSON><PERSON><PERSON>', 98.52941176470588],\n", " ['<PERSON><PERSON><PERSON><PERSON> osseus', 98.55491329479769],\n", " ['<PERSON><PERSON><PERSON> cure<PERSON>', 98.57142857142858],\n", " ['Epinephelus itajara', 98.57142857142858],\n", " ['Mycteroperca microlepis', 98.58490566037736],\n", " ['Hypanus sabinus', 98.66962305986696],\n", " ['Le<PERSON><PERSON> microlophus', 98.67549668874173],\n", " ['Pomatomus saltatrix', 98.6784140969163],\n", " ['<PERSON><PERSON><PERSON><PERSON> analis', 98.68073878627969],\n", " ['<PERSON><PERSON><PERSON> gibbosus', 98.68421052631578],\n", " ['Lachnolaimus maximus', 98.7012987012987],\n", " ['<PERSON><PERSON><PERSON><PERSON><PERSON> maculatus', 98.72611464968153],\n", " ['Sarda sarda', 98.81656804733728],\n", " ['Aeto<PERSON><PERSON> narinari', 98.83720930232558],\n", " ['Platycephalus fuscus', 98.87218045112782],\n", " ['Pomoxis nigromaculatus', 98.87640449438202],\n", " ['Albula vulpes', 98.88888888888889],\n", " ['T<PERSON><PERSON><PERSON> carolinus', 98.89135254988913],\n", " ['Bagre marinus', 98.89705882352942],\n", " ['<PERSON>pt<PERSON> punctulatus', 98.89705882352942],\n", " ['<PERSON><PERSON> ciliaris', 98.91891891891892],\n", " ['Her<PERSON>thy<PERSON> cyanoguttatus', 98.95615866388309],\n", " ['Caranx ignobilis', 98.9655172413793],\n", " ['Scardinius erythrophthalmus', 98.98477157360406],\n", " ['<PERSON><PERSON><PERSON> scombrus', 98.99497487437185],\n", " ['<PERSON><PERSON><PERSON> brama', 99.02912621359224],\n", " ['<PERSON><PERSON><PERSON> font<PERSON>', 99.04397705544933],\n", " ['<PERSON><PERSON><PERSON> megalot<PERSON>', 99.05660377358491],\n", " ['<PERSON><PERSON><PERSON><PERSON> guttata', 99.05660377358491],\n", " ['Macquaria ambigua', 99.08256880733946],\n", " ['Morone americana', 99.08536585365853],\n", " ['Scomb<PERSON>morus cavalla', 99.09638554216868],\n", " ['Epinephelus tukula', 99.09909909909909],\n", " ['<PERSON><PERSON><PERSON><PERSON> fulviflam<PERSON>', 99.10714285714286],\n", " ['Lutjanus apodus', 99.10913140311804],\n", " ['<PERSON><PERSON> saxatilis', 99.11894273127754],\n", " ['Archosargus probatocephalus', 99.12087912087912],\n", " ['<PERSON><PERSON><PERSON><PERSON> griseus', 99.12280701754386],\n", " ['Hypophthalmichthys molitrix', 99.12280701754386],\n", " ['<PERSON><PERSON><PERSON> tiburo', 99.14163090128756],\n", " ['<PERSON><PERSON><PERSON> onitis', 99.14529914529915],\n", " ['<PERSON><PERSON><PERSON> melanops', 99.14529914529915],\n", " ['Caranx sexfasciatus', 99.15254237288136],\n", " ['Scarus gua<PERSON>', 99.15966386554622],\n", " ['Plectropomus laevis', 99.16666666666667],\n", " ['<PERSON><PERSON><PERSON> pollachius', 99.16666666666667],\n", " ['Paralabrax nebulifer', 99.16666666666667],\n", " ['Gymnosarda unicolor', 99.18032786885246],\n", " ['Sparisoma rubripinne', 99.18032786885246],\n", " ['Megalops atlanticus', 99.1825613079019],\n", " ['Acipenser transmontanus', 99.19354838709677],\n", " ['Carcharhinus melanopterus', 99.19354838709677],\n", " ['Anisotremus surinamensis', 99.2],\n", " ['Stenotomus chryso<PERSON>', 99.2248062015504],\n", " ['Ctenopharyngodon idella', 99.23954372623575],\n", " ['<PERSON><PERSON><PERSON><PERSON><PERSON> miniata', 99.25373134328358],\n", " ['Nocomis micropogon', 99.25373134328358],\n", " ['<PERSON><PERSON><PERSON><PERSON> striata', 99.26470588235294],\n", " ['<PERSON><PERSON><PERSON><PERSON> fasciatus', 99.29078014184397],\n", " ['Acanth<PERSON> chirurgus', 99.3006993006993],\n", " ['<PERSON><PERSON><PERSON><PERSON><PERSON> regalis', 99.30555555555556],\n", " ['Aplodinotus grunniens', 99.32279909706546],\n", " ['Perca flavescens', 99.32584269662922],\n", " ['<PERSON><PERSON><PERSON> spa<PERSON>i', 99.33333333333333],\n", " ['Mycteroperca bonaci', 99.3421052631579],\n", " ['<PERSON><PERSON><PERSON><PERSON> macropterus', 99.3421052631579],\n", " ['Canthigaster rostrata', 99.3421052631579],\n", " ['<PERSON><PERSON><PERSON><PERSON> campechanus', 99.34426229508196],\n", " ['<PERSON><PERSON><PERSON> hippur<PERSON>', 99.35622317596567],\n", " ['<PERSON><PERSON><PERSON><PERSON> ocellatus', 99.4392523364486],\n", " ['Caranx crysos', 99.43977591036415],\n", " ['<PERSON><PERSON><PERSON> plumierii', 99.44444444444444],\n", " ['Caranx latus', 99.45945945945947],\n", " ['Epinephelus morio', 99.47089947089947],\n", " ['Acanthocybium solandri', 99.47229551451187],\n", " ['Mugil cephalus', 99.47780678851174],\n", " ['Tinca tinca', 99.50372208436724],\n", " ['<PERSON><PERSON><PERSON><PERSON> falcatus', 99.52718676122932],\n", " ['<PERSON><PERSON><PERSON> cyanellus', 99.54441913439635],\n", " ['<PERSON><PERSON> vitreus', 99.54648526077098],\n", " ['<PERSON><PERSON><PERSON> ocellaris', 99.55654101995566],\n", " ['Orthopristis chrysoptera', 99.5575221238938],\n", " ['Acanthopagrus australis', 99.55849889624724],\n", " ['Mayaheros urophthalmus', 99.56043956043956],\n", " ['<PERSON><PERSON><PERSON> auritus', 99.57446808510639],\n", " ['Lobotes surinamensis', 99.57805907172997],\n", " ['<PERSON><PERSON><PERSON><PERSON> argentimaculatus', 99.57805907172997],\n", " ['<PERSON><PERSON><PERSON><PERSON><PERSON> cruentata', 99.57983193277312],\n", " ['Rachycentron canadum', 99.58333333333333],\n", " ['<PERSON><PERSON><PERSON> cromis', 99.59349593495935],\n", " ['Parachromis managuensis', 99.61832061068702],\n", " ['Dicentrarchus labrax', 99.67320261437908],\n", " ['Elops saurus', 99.73262032085562],\n", " ['Cymatogaster aggregata', 99.77375565610859],\n", " ['Lagodon rhomboides', 99.7752808988764],\n", " ['Micropogon<PERSON> undulatus', 99.77578475336323],\n", " ['Per<PERSON> fluviatilis', 99.77728285077951],\n", " ['Catostomus commersonii', 99.77827050997783],\n", " ['Centropo<PERSON> undecimalis', 100.0],\n", " ['Anisotremus virginicus', 100.0],\n", " ['Pterois volitans', 100.0],\n", " ['Rhincodon typus', 100.0],\n", " ['<PERSON><PERSON><PERSON> gladius', 100.0],\n", " ['Ocyurus chrysurus', 100.0],\n", " ['Bali<PERSON>s capriscus', 100.0],\n", " ['<PERSON><PERSON><PERSON><PERSON> xanthurus', 100.0],\n", " ['<PERSON><PERSON><PERSON><PERSON><PERSON> saxatil<PERSON>', 100.0],\n", " ['<PERSON><PERSON><PERSON> bipinnulata', 100.0],\n", " ['Bodianus rufus', 100.0],\n", " ['Opisthonema oglinum', 100.0],\n", " ['Cynoscion nebulosus', 100.0],\n", " ['Synodus foetens', 100.0],\n", " ['Menidia menidia', 100.0],\n", " ['Enneacanthus gloriosus', 100.0],\n", " ['Selene vomer', 100.0],\n", " ['Prionotus evolans', 100.0],\n", " ['Ambloplites rupestris', 100.0],\n", " ['Hippoglossus stenolepis', 100.0],\n", " ['<PERSON><PERSON><PERSON>', 100.0],\n", " ['Paralabrax clathratus', 100.0],\n", " ['<PERSON><PERSON><PERSON><PERSON> ma<PERSON>', 100.0],\n", " ['Lates calcarifer', 100.0],\n", " ['Morone mississippiensis', 100.0],\n", " ['Lutjanus decussatus', 100.0],\n", " ['<PERSON><PERSON><PERSON><PERSON> fulvus', 100.0],\n", " ['Lutjanus gibbus', 100.0],\n", " ['Variola louti', 100.0],\n", " ['<PERSON>tronotus ocellatus', 100.0],\n", " ['<PERSON><PERSON><PERSON> semifasciata', 100.0],\n", " ['Heterodontus port<PERSON>i', 100.0],\n", " ['Ictiobus bubalus', 100.0],\n", " ['Pagrus auratus', 100.0],\n", " ['Semotilus corporalis', 100.0],\n", " ['Channa argus', 100.0],\n", " ['Dorosoma cepedianum', 100.0],\n", " ['Rhabdosargus sarba', 100.0],\n", " ['Amphistichus argenteus', 100.0],\n", " ['Gnathanodon speciosus', 100.0],\n", " ['Sphoeroides maculatus', 100.0],\n", " ['<PERSON><PERSON><PERSON><PERSON><PERSON> peelii', 100.0],\n", " ['<PERSON><PERSON><PERSON> miniatus', 100.0],\n", " ['Sparisoma aurofrenatum', 100.0],\n", " ['<PERSON><PERSON><PERSON><PERSON> pullus', 100.0],\n", " ['Acroteriobatus annulatus', 100.0]]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["accuracy_list"]}], "metadata": {"kernelspec": {"display_name": "NEMO", "language": "python", "name": "nemo"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 5}