{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0cbf27c7", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "#Change path specificly to your directories\n", "sys.path.insert(1, '/home/<USER>/Fishial/Object-Detection-Model')\n", "                \n", "import cv2\n", "import json\n", "import copy\n", "import pandas\n", "import random\n", "import argparse\n", "import requests\n", "import numpy as np\n", "from PIL import Image\n", "\n", "from tqdm import tqdm\n", "from torchvision import transforms\n", "from concurrent.futures import ThreadPoolExecutor\n", "\n", "from os import listdir, walk\n", "from os.path import isfile, join\n", "from module.classification_package.src.utils import read_json, save_json\n", "from module.classification_package.src.dataset import FishialDataset\n", "from module.segmentation_package.src.utils import get_mask\n", "\n", "from shapely.geometry import Point\n", "from shapely.geometry.polygon import Polygon\n", "\n", "import matplotlib.pyplot as plt\n", "# import FiftyOne\n", "import fiftyone as fo\n", "import fiftyone.zoo as foz\n", "import fiftyone.brain as fob\n"]}, {"cell_type": "code", "execution_count": 108, "id": "d1ff7ce1", "metadata": {}, "outputs": [], "source": ["def get_category_name(data):\n", "    category_ids = {}\n", "    for idx, i in enumerate(data['categories']):\n", "        if i['name'] == 'General body shape':\n", "            if i['id'] not in category_ids:\n", "                category_ids.update({\n", "                    i['id']: {\n", "                        'name': i['supercategory'],\n", "                        'cnt': 0\n", "                    }\n", "                })\n", "            else:\n", "                category_ids[i['id']]['cnt'] += 1\n", "    return category_ids\n", "\n", "def get_category_cnt(data):\n", "    category_cnt = {}\n", "    for i in data['annotations']:\n", "        if 'category_id' in i:\n", "            if i['category_id'] not in category_cnt:\n", "                category_cnt.update({\n", "                    i['category_id']: {\n", "                        'cnt': 0\n", "                    }\n", "                })\n", "            else:\n", "                category_cnt[i['category_id']]['cnt'] += 1\n", "    return category_cnt\n", "\n", "def get_class_with_min_ann(data, min_ann = 50):\n", "    data_dict = []\n", "    for i in data:\n", "        if data[i]['cnt'] >= min_ann:\n", "            data_dict.append([i, data[i]['cnt']])\n", "    return data_dict\n", "\n", "def find_image_by_id(id: int):\n", "    for i in data['images']:\n", "        if i['id'] == id:\n", "            return i\n", "\n", "def get_list_of_files_in_folder(path):\n", "    list_of_files = []\n", "    for (dirpath, dirnames, filenames) in walk(path):\n", "        list_of_files.extend(filenames)\n", "        break\n", "    return list_of_files\n", "\n", "def download(url):\n", "    r = requests.get(url[0], allow_redirects=True)  # to get content after redirection\n", "    with open(url[1], 'wb') as f:\n", "        f.write(r.content)\n", "    print(\"Current: {}\".format(url[2]), end='\\r')\n", "    \n", "def get_image(data, folder_main, id):\n", "    for img in data['images']:\n", "        if img['id'] == id:\n", "            return cv2.imread(os.path.join(folder_main, img['file_name']))\n", "        \n", "def get_valid_category(data):\n", "    valid_category = {}\n", "    for z in data['categories']:\n", "        if z['name'] == 'General body shape' and z['supercategory'] != 'unknown':\n", "            valid_category.update({z['id']: z['supercategory']})\n", "    return valid_category\n", "\n", "def get_all_ann_by_img_id(data_full, img_id, valid_category):\n", "    list_off_ann_for_specific_image = []\n", "    for i in data_full['annotations']:\n", "        try:\n", "            if i['image_id'] == img_id and i['category_id'] in valid_category:\n", "                list_off_ann_for_specific_image.append(i)\n", "        except:\n", "            pass\n", "    return list_off_ann_for_specific_image\n", "\n", "def get_mask_by_ann(data, ann, main_folder, box = False):\n", "    polygon_tmp = []\n", "    for pt in range(int(len(ann['segmentation'][0])/2)):\n", "        polygon_tmp.append([int(ann['segmentation'][0][pt * 2]), int(ann['segmentation'][0][pt * 2 + 1])])\n", "\n", "    img = get_image(data, main_folder, ann['image_id'])\n", "    if box:\n", "        rect = cv2.boundingRect(np.array(polygon_tmp))\n", "        x, y, w, h = rect\n", "        mask = img[y:y + h, x:x + w].copy()\n", "        if len(mask) == 0:\n", "            return None\n", "    else:\n", "        mask = get_mask(img, np.array(polygon_tmp))\n", "    \n", "    return mask\n", "\n", "def fix_poly(poly, shape):\n", "    poly = [ (min(max(0, point[0]), shape[1]), min(max(0, point[1]), shape[1])) for point in poly]\n", "    return poly\n", "\n", "def <PERSON>y<PERSON><PERSON>(x,y):\n", "    return 0.5*np.abs(np.dot(x,np.roll(y,1))-np.dot(y,np.roll(x,1)))\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f134d148", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}], "source": ["dst_path = \"/home/<USER>/Fishial/dataset/fishial_collection/data\"\n", "path_to_src_coco_json =  '/home/<USER>/Fishial/dataset/export/fixed_invalid.json'\n", "path_to_new_dataset = \"/home/<USER>/Fishial/dataset/classification/fo_data\"\n", "\n", "min_eval_img = 15\n", "max_percent_eva_img = 0.2\n", "max_cnt_img_per_class = 350\n", "min_cnt_img = 50\n", "\n", "list_of_files = get_list_of_files_in_folder(dst_path)\n", "data_full = read_json(path_to_src_coco_json)\n", "\n", "folder_to_save_files = os.path.join(path_to_new_dataset, 'data')\n", "os.makedirs(os.path.join(path_to_new_dataset, 'data'), exist_ok=True)"]}, {"cell_type": "code", "execution_count": 4, "id": "8a1cd3d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n", "3\n", "4\n", "5\n", "6eft: 97305/97306\n"]}], "source": ["list_of_valid_img = {}\n", "list_of_files = get_list_of_files_in_folder(dst_path)\n", "\n", "for i in data_full['images']:\n", "    if 'is_invalid' in i:\n", "        if i['is_invalid']: continue\n", "\n", "    if i['file_name'] in list_of_files:\n", "        list_of_valid_img.update({i['id']: i})\n", "        \n", "valid_category = get_valid_category(data_full)\n", "ann_per_dict = {valid_category[category]: [] for category in valid_category}\n", "\n", "for ann_id, ann_class in enumerate(data_full['annotations']):\n", "    print(f\"Left: {ann_id}/{len(data_full['annotations'])}\", end='\\r')\n", "    try:\n", "        if not ann_class['image_id'] in list_of_valid_img: continue\n", "        if not ann_class['category_id'] in valid_category: continue\n", "        if ann_class['is_valid'] == False: continue\n", "    except:\n", "        continue\n", "        \n", "    poly = [(\n", "        int(ann_class['segmentation'][0][point * 2]), \n", "        int(ann_class['segmentation'][0][point * 2 + 1])) for point in range(int(len(ann_class['segmentation'][0])/2))]\n", "    ann_class['segmentation'] = poly\n", "    ann_class.update({'include_in_odm': list_of_valid_img[ann_class['image_id']]['fishial_extra']['include_in_odm'] })\n", "    ann_per_dict[valid_category[ann_class['category_id']]].append(ann_class)"]}, {"cell_type": "code", "execution_count": 5, "id": "05c4d74f", "metadata": {}, "outputs": [], "source": ["for k in list(ann_per_dict):\n", "    if len(ann_per_dict[k]) < min_cnt_img:\n", "        del ann_per_dict[k]"]}, {"cell_type": "code", "execution_count": 6, "id": "32d975b0", "metadata": {"scrolled": false}, "outputs": [], "source": ["labels_dict = {}\n", "labels_dict.update({\n", "            label: label_id for label_id, label in enumerate(list(ann_per_dict))\n", "        })\n", "\n", "data_compleated = [[],[],[]]\n", "\n", "for label_name in ann_per_dict:\n", "    ann_per_dict[label_name] = sorted(ann_per_dict[label_name], key=lambda d: d['include_in_odm'], reverse=True) \n", "    id_end_val = int(max_percent_eva_img * len(ann_per_dict[label_name])) if len(ann_per_dict[label_name]) > int(min_eval_img/max_percent_eva_img) else min_eval_img\n", "    id_end_train = len(ann_per_dict[label_name]) - id_end_val - 1 if len(ann_per_dict[label_name]) - id_end_val <= max_cnt_img_per_class else max_cnt_img_per_class\n", "    data_compleated[0].extend(ann_per_dict[label_name][:id_end_val])\n", "    data_compleated[1].extend(ann_per_dict[label_name][id_end_val:id_end_val + id_end_train])\n", "    data_compleated[2].extend(ann_per_dict[label_name][id_end_val + id_end_train:])\n", "\n", "for data in data_compleated:\n", "    for k in data:\n", "        k.update({'id_internal': labels_dict[valid_category[k['category_id']]]})\n", "        k.update({'label': valid_category[k['category_id']]})\n", "        "]}, {"cell_type": "code", "execution_count": 7, "id": "7f75102d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dataset: rest | 3688/3689461\r"]}], "source": ["\n", "\n", "samples = []\n", "records = []\n", "for dataset in zip(data_compleated, ['val', 'train', 'rest']):\n", "    for k_id, ann_inst in enumerate(dataset[0]):\n", "        print(f\"dataset: {dataset[1]} | {k_id}/{len(dataset[0])}\", end='\\r')\n", "                    \n", "        img = cv2.imread(os.path.join(dst_path, list_of_valid_img[ann_inst['image_id']]['file_name']))\n", "        shape = img.shape\n", "        if shape[0] > 2400 or shape[1] > 2400: continue\n", "        \n", "        ann_inst['segmentation'] = fix_poly(ann_inst['segmentation'], shape)\n", "        rect = cv2.boundingRect(np.array(ann_inst['segmentation']))\n", "        x, y, w, h = rect\n", "        if w < 80 or h < 80: continue\n", "        \n", "        mask = img[y:y + h, x:x + w]\n", "        ann_inst['segmentation'] = [(v[0] - x, v[1] - y) for v in ann_inst['segmentation']]\n", "        \n", "        ann_id = ann_inst['id']\n", "        \n", "        path_to_save = os.path.join(folder_to_save_files, ann_id + \".png\")\n", "        try:\n", "            cv2.imwrite(path_to_save, mask)\n", "        except:\n", "            continue\n", "\n", "        new_poly = [(z[0]/w, z[1]/h) for z in ann_inst['segmentation']]\n", "\n", "        tag_odm = 'odm_true' if ann_inst['include_in_odm'] else 'odm_false'\n", "        records.append(ann_inst)\n", "        sample = fo.Sample(filepath=path_to_save, tags=[dataset[1], tag_odm])\n", "        sample[\"polyline\"] = fo.Polyline(\n", "                                label=ann_inst['label'],\n", "                                points=[new_poly],\n", "                                closed=True,\n", "                                filled=False)\n", "        \n", "        sample[\"area\"] = PolyArea(new_poly[:,0],new_poly[:,1])\n", "        sample['width'] = w\n", "        sample['height'] = h\n", "        sample[\"annotation_id\"] = ann_id\n", "        sample[\"image_id\"] = str(ann_inst['image_id'])\n", "        samples.append(sample)\n", "save_json(records, os.path.join(path_to_new_dataset, \"annotation.json\"))"]}, {"cell_type": "code", "execution_count": 8, "id": "ac62a696", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 100% |█████████████| 34484/34484 [1.1m elapsed, 0s remaining, 629.7 samples/s]       \n"]}, {"data": {"text/plain": ["['6380bc22ccb46092e99addc0',\n", " '6380bc22ccb46092e99addc2',\n", " '6380bc22ccb46092e99addc3',\n", " '6380bc22ccb46092e99addc6',\n", " '6380bc22ccb46092e99addc7',\n", " '6380bc22ccb46092e99addc8',\n", " '6380bc22ccb46092e99addc9',\n", " '6380bc22ccb46092e99addce',\n", " '6380bc22ccb46092e99addcf',\n", " '6380bc22ccb46092e99addd0',\n", " '6380bc22ccb46092e99addd1',\n", " '6380bc22ccb46092e99addd2',\n", " '6380bc22ccb46092e99addd3',\n", " '6380bc22ccb46092e99addd4',\n", " '6380bc22ccb46092e99addd5',\n", " '6380bc22ccb46092e99addde',\n", " '6380bc22ccb46092e99adddf',\n", " '6380bc22ccb46092e99adde0',\n", " '6380bc22ccb46092e99adde1',\n", " '6380bc22ccb46092e99adde2',\n", " '6380bc22ccb46092e99adde3',\n", " '6380bc22ccb46092e99adde4',\n", " '6380bc22ccb46092e99adde5',\n", " '6380bc22ccb46092e99adde6',\n", " '6380bc22ccb46092e99adde7',\n", " '6380bc22ccb46092e99adde8',\n", " '6380bc22ccb46092e99adde9',\n", " '6380bc22ccb46092e99addea',\n", " '6380bc22ccb46092e99addeb',\n", " '6380bc22ccb46092e99addec',\n", " '6380bc22ccb46092e99added',\n", " '6380bc22ccb46092e99addfe',\n", " '6380bc22ccb46092e99addff',\n", " '6380bc22ccb46092e99ade00',\n", " '6380bc22ccb46092e99ade01',\n", " '6380bc22ccb46092e99ade02',\n", " '6380bc22ccb46092e99ade03',\n", " '6380bc22ccb46092e99ade04',\n", " '6380bc22ccb46092e99ade05',\n", " '6380bc22ccb46092e99ade06',\n", " '6380bc22ccb46092e99ade07',\n", " '6380bc22ccb46092e99ade08',\n", " '6380bc22ccb46092e99ade09',\n", " '6380bc22ccb46092e99ade0a',\n", " '6380bc22ccb46092e99ade0b',\n", " '6380bc22ccb46092e99ade0c',\n", " '6380bc22ccb46092e99ade0d',\n", " '6380bc22ccb46092e99ade0e',\n", " '6380bc22ccb46092e99ade0f',\n", " '6380bc22ccb46092e99ade10',\n", " '6380bc22ccb46092e99ade11',\n", " '6380bc22ccb46092e99ade12',\n", " '6380bc22ccb46092e99ade13',\n", " '6380bc22ccb46092e99ade14',\n", " '6380bc22ccb46092e99ade15',\n", " '6380bc22ccb46092e99ade16',\n", " '6380bc22ccb46092e99ade17',\n", " '6380bc22ccb46092e99ade18',\n", " '6380bc22ccb46092e99ade19',\n", " '6380bc22ccb46092e99ade1a',\n", " '6380bc22ccb46092e99ade1b',\n", " '6380bc22ccb46092e99ade1c',\n", " '6380bc22ccb46092e99ade1d',\n", " '6380bc22ccb46092e99ade3e',\n", " '6380bc22ccb46092e99ade3f',\n", " '6380bc22ccb46092e99ade40',\n", " '6380bc22ccb46092e99ade41',\n", " '6380bc22ccb46092e99ade42',\n", " '6380bc22ccb46092e99ade43',\n", " '6380bc22ccb46092e99ade44',\n", " '6380bc22ccb46092e99ade45',\n", " '6380bc22ccb46092e99ade46',\n", " '6380bc22ccb46092e99ade47',\n", " '6380bc22ccb46092e99ade48',\n", " '6380bc22ccb46092e99ade49',\n", " '6380bc22ccb46092e99ade4a',\n", " '6380bc22ccb46092e99ade4b',\n", " '6380bc22ccb46092e99ade4c',\n", " '6380bc22ccb46092e99ade4d',\n", " '6380bc22ccb46092e99ade4e',\n", " '6380bc22ccb46092e99ade4f',\n", " '6380bc22ccb46092e99ade50',\n", " '6380bc22ccb46092e99ade51',\n", " '6380bc22ccb46092e99ade52',\n", " '6380bc22ccb46092e99ade53',\n", " '6380bc22ccb46092e99ade54',\n", " '6380bc22ccb46092e99ade55',\n", " '6380bc22ccb46092e99ade56',\n", " '6380bc22ccb46092e99ade57',\n", " '6380bc22ccb46092e99ade58',\n", " '6380bc22ccb46092e99ade59',\n", " '6380bc22ccb46092e99ade5a',\n", " '6380bc22ccb46092e99ade5b',\n", " '6380bc22ccb46092e99ade5c',\n", " '6380bc22ccb46092e99ade5d',\n", " '6380bc22ccb46092e99ade5e',\n", " '6380bc22ccb46092e99ade5f',\n", " '6380bc22ccb46092e99ade60',\n", " '6380bc22ccb46092e99ade61',\n", " '6380bc22ccb46092e99ade62',\n", " '6380bc22ccb46092e99ade63',\n", " '6380bc22ccb46092e99ade64',\n", " '6380bc22ccb46092e99ade65',\n", " '6380bc22ccb46092e99ade66',\n", " '6380bc22ccb46092e99ade67',\n", " '6380bc22ccb46092e99ade68',\n", " '6380bc22ccb46092e99ade69',\n", " '6380bc22ccb46092e99ade6a',\n", " '6380bc22ccb46092e99ade6b',\n", " '6380bc22ccb46092e99ade6c',\n", " '6380bc22ccb46092e99ade6d',\n", " '6380bc22ccb46092e99ade6e',\n", " '6380bc22ccb46092e99ade6f',\n", " '6380bc22ccb46092e99ade70',\n", " '6380bc22ccb46092e99ade71',\n", " '6380bc22ccb46092e99ade72',\n", " '6380bc22ccb46092e99ade73',\n", " '6380bc22ccb46092e99ade74',\n", " '6380bc22ccb46092e99ade75',\n", " '6380bc22ccb46092e99ade76',\n", " '6380bc22ccb46092e99ade77',\n", " '6380bc22ccb46092e99ade78',\n", " '6380bc22ccb46092e99ade79',\n", " '6380bc22ccb46092e99ade7a',\n", " '6380bc22ccb46092e99ade7b',\n", " '6380bc22ccb46092e99ade7c',\n", " '6380bc22ccb46092e99ade7d',\n", " '6380bc23ccb46092e99adebe',\n", " '6380bc23ccb46092e99adebf',\n", " '6380bc23ccb46092e99adec0',\n", " '6380bc23ccb46092e99adec1',\n", " '6380bc23ccb46092e99adec2',\n", " '6380bc23ccb46092e99adec3',\n", " '6380bc23ccb46092e99adec4',\n", " '6380bc23ccb46092e99adec5',\n", " '6380bc23ccb46092e99adec6',\n", " '6380bc23ccb46092e99adec7',\n", " '6380bc23ccb46092e99adec8',\n", " '6380bc23ccb46092e99adec9',\n", " '6380bc23ccb46092e99adeca',\n", " '6380bc23ccb46092e99adecb',\n", " '6380bc23ccb46092e99adecc',\n", " '6380bc23ccb46092e99adecd',\n", " '6380bc23ccb46092e99adece',\n", " '6380bc23ccb46092e99adecf',\n", " '6380bc23ccb46092e99aded0',\n", " '6380bc23ccb46092e99aded1',\n", " '6380bc23ccb46092e99aded2',\n", " '6380bc23ccb46092e99aded3',\n", " '6380bc23ccb46092e99aded4',\n", " '6380bc23ccb46092e99aded5',\n", " '6380bc23ccb46092e99aded6',\n", " '6380bc23ccb46092e99aded7',\n", " '6380bc23ccb46092e99aded8',\n", " '6380bc23ccb46092e99aded9',\n", " '6380bc23ccb46092e99adeda',\n", " '6380bc23ccb46092e99adedb',\n", " '6380bc23ccb46092e99adedc',\n", " '6380bc23ccb46092e99adedd',\n", " '6380bc23ccb46092e99adede',\n", " '6380bc23ccb46092e99adedf',\n", " '6380bc23ccb46092e99adee0',\n", " '6380bc23ccb46092e99adee1',\n", " '6380bc23ccb46092e99adee2',\n", " '6380bc23ccb46092e99adee3',\n", " '6380bc23ccb46092e99adee4',\n", " '6380bc23ccb46092e99adee5',\n", " '6380bc23ccb46092e99adee6',\n", " '6380bc23ccb46092e99adee7',\n", " '6380bc23ccb46092e99adee8',\n", " '6380bc23ccb46092e99adee9',\n", " '6380bc23ccb46092e99adeea',\n", " '6380bc23ccb46092e99adeeb',\n", " '6380bc23ccb46092e99adeec',\n", " '6380bc23ccb46092e99adeed',\n", " '6380bc23ccb46092e99adeee',\n", " '6380bc23ccb46092e99adeef',\n", " '6380bc23ccb46092e99adef0',\n", " '6380bc23ccb46092e99adef1',\n", " '6380bc23ccb46092e99adef2',\n", " '6380bc23ccb46092e99adef3',\n", " '6380bc23ccb46092e99adef4',\n", " '6380bc23ccb46092e99adef5',\n", " '6380bc23ccb46092e99adef6',\n", " '6380bc23ccb46092e99adef7',\n", " '6380bc23ccb46092e99adef8',\n", " '6380bc23ccb46092e99adef9',\n", " '6380bc23ccb46092e99adefa',\n", " '6380bc23ccb46092e99adefb',\n", " '6380bc23ccb46092e99adefc',\n", " '6380bc23ccb46092e99adefd',\n", " '6380bc23ccb46092e99adefe',\n", " '6380bc23ccb46092e99adeff',\n", " '6380bc23ccb46092e99adf00',\n", " '6380bc23ccb46092e99adf01',\n", " '6380bc23ccb46092e99adf02',\n", " '6380bc23ccb46092e99adf03',\n", " '6380bc23ccb46092e99adf04',\n", " '6380bc23ccb46092e99adf05',\n", " '6380bc23ccb46092e99adf06',\n", " '6380bc23ccb46092e99adf07',\n", " '6380bc23ccb46092e99adf08',\n", " '6380bc23ccb46092e99adf09',\n", " '6380bc23ccb46092e99adf0a',\n", " '6380bc23ccb46092e99adf0b',\n", " '6380bc23ccb46092e99adf0c',\n", " '6380bc23ccb46092e99adf0d',\n", " '6380bc23ccb46092e99adf0e',\n", " '6380bc23ccb46092e99adf0f',\n", " '6380bc23ccb46092e99adf10',\n", " '6380bc23ccb46092e99adf11',\n", " '6380bc23ccb46092e99adf12',\n", " '6380bc23ccb46092e99adf13',\n", " '6380bc23ccb46092e99adf14',\n", " '6380bc23ccb46092e99adf15',\n", " '6380bc23ccb46092e99adf16',\n", " '6380bc23ccb46092e99adf17',\n", " '6380bc23ccb46092e99adf18',\n", " '6380bc23ccb46092e99adf19',\n", " '6380bc23ccb46092e99adf1a',\n", " '6380bc23ccb46092e99adf1b',\n", " '6380bc23ccb46092e99adf1c',\n", " '6380bc23ccb46092e99adf1d',\n", " '6380bc23ccb46092e99adf1e',\n", " '6380bc23ccb46092e99adf1f',\n", " '6380bc23ccb46092e99adf20',\n", " '6380bc23ccb46092e99adf21',\n", " '6380bc23ccb46092e99adf22',\n", " '6380bc23ccb46092e99adf23',\n", " '6380bc23ccb46092e99adf24',\n", " '6380bc23ccb46092e99adf25',\n", " '6380bc23ccb46092e99adf26',\n", " '6380bc23ccb46092e99adf27',\n", " '6380bc23ccb46092e99adf28',\n", " '6380bc23ccb46092e99adf29',\n", " '6380bc23ccb46092e99adf2a',\n", " '6380bc23ccb46092e99adf2b',\n", " '6380bc23ccb46092e99adf2c',\n", " '6380bc23ccb46092e99adf2d',\n", " '6380bc23ccb46092e99adf2e',\n", " '6380bc23ccb46092e99adf2f',\n", " '6380bc23ccb46092e99adf30',\n", " '6380bc23ccb46092e99adf31',\n", " '6380bc23ccb46092e99adf32',\n", " '6380bc23ccb46092e99adf33',\n", " '6380bc23ccb46092e99adf34',\n", " '6380bc23ccb46092e99adf35',\n", " '6380bc23ccb46092e99adf36',\n", " '6380bc23ccb46092e99adf37',\n", " '6380bc23ccb46092e99adf38',\n", " '6380bc23ccb46092e99adf39',\n", " '6380bc23ccb46092e99adf3a',\n", " '6380bc23ccb46092e99adf3b',\n", " '6380bc23ccb46092e99adf3c',\n", " '6380bc23ccb46092e99adf3d',\n", " '6380bc23ccb46092e99adfbe',\n", " '6380bc23ccb46092e99adfbf',\n", " '6380bc23ccb46092e99adfc0',\n", " '6380bc23ccb46092e99adfc1',\n", " '6380bc23ccb46092e99adfc2',\n", " '6380bc23ccb46092e99adfc3',\n", " '6380bc23ccb46092e99adfc4',\n", " '6380bc23ccb46092e99adfc5',\n", " '6380bc23ccb46092e99adfc6',\n", " '6380bc23ccb46092e99adfc7',\n", " '6380bc23ccb46092e99adfc8',\n", " '6380bc23ccb46092e99adfc9',\n", " '6380bc23ccb46092e99adfca',\n", " '6380bc23ccb46092e99adfcb',\n", " '6380bc23ccb46092e99adfcc',\n", " '6380bc23ccb46092e99adfcd',\n", " '6380bc23ccb46092e99adfce',\n", " '6380bc23ccb46092e99adfcf',\n", " '6380bc23ccb46092e99adfd0',\n", " '6380bc23ccb46092e99adfd1',\n", " '6380bc23ccb46092e99adfd2',\n", " '6380bc23ccb46092e99adfd3',\n", " '6380bc23ccb46092e99adfd4',\n", " '6380bc23ccb46092e99adfd5',\n", " '6380bc23ccb46092e99adfd6',\n", " '6380bc23ccb46092e99adfd7',\n", " '6380bc23ccb46092e99adfd8',\n", " '6380bc23ccb46092e99adfd9',\n", " '6380bc23ccb46092e99adfda',\n", " '6380bc23ccb46092e99adfdb',\n", " '6380bc23ccb46092e99adfdc',\n", " '6380bc23ccb46092e99adfdd',\n", " '6380bc23ccb46092e99adfde',\n", " '6380bc23ccb46092e99adfdf',\n", " '6380bc23ccb46092e99adfe0',\n", " '6380bc23ccb46092e99adfe1',\n", " '6380bc23ccb46092e99adfe2',\n", " '6380bc23ccb46092e99adfe3',\n", " '6380bc23ccb46092e99adfe4',\n", " '6380bc23ccb46092e99adfe5',\n", " '6380bc23ccb46092e99adfe6',\n", " '6380bc23ccb46092e99adfe7',\n", " '6380bc23ccb46092e99adfe8',\n", " '6380bc23ccb46092e99adfe9',\n", " '6380bc23ccb46092e99adfea',\n", " '6380bc23ccb46092e99adfeb',\n", " '6380bc23ccb46092e99adfec',\n", " '6380bc23ccb46092e99adfed',\n", " '6380bc23ccb46092e99adfee',\n", " '6380bc23ccb46092e99adfef',\n", " '6380bc23ccb46092e99adff0',\n", " '6380bc23ccb46092e99adff1',\n", " '6380bc23ccb46092e99adff2',\n", " '6380bc23ccb46092e99adff3',\n", " '6380bc23ccb46092e99adff4',\n", " '6380bc23ccb46092e99adff5',\n", " '6380bc23ccb46092e99adff6',\n", " '6380bc23ccb46092e99adff7',\n", " '6380bc23ccb46092e99adff8',\n", " '6380bc23ccb46092e99adff9',\n", " '6380bc23ccb46092e99adffa',\n", " '6380bc23ccb46092e99adffb',\n", " '6380bc23ccb46092e99adffc',\n", " '6380bc23ccb46092e99adffd',\n", " '6380bc23ccb46092e99ae03e',\n", " '6380bc23ccb46092e99ae03f',\n", " '6380bc23ccb46092e99ae040',\n", " '6380bc23ccb46092e99ae041',\n", " '6380bc23ccb46092e99ae042',\n", " '6380bc23ccb46092e99ae043',\n", " '6380bc23ccb46092e99ae044',\n", " '6380bc23ccb46092e99ae045',\n", " '6380bc23ccb46092e99ae046',\n", " '6380bc23ccb46092e99ae047',\n", " '6380bc23ccb46092e99ae048',\n", " '6380bc23ccb46092e99ae049',\n", " '6380bc23ccb46092e99ae04a',\n", " '6380bc23ccb46092e99ae04b',\n", " '6380bc23ccb46092e99ae04c',\n", " '6380bc23ccb46092e99ae04d',\n", " '6380bc23ccb46092e99ae04e',\n", " '6380bc23ccb46092e99ae04f',\n", " '6380bc23ccb46092e99ae050',\n", " '6380bc23ccb46092e99ae051',\n", " '6380bc23ccb46092e99ae052',\n", " '6380bc23ccb46092e99ae053',\n", " '6380bc23ccb46092e99ae054',\n", " '6380bc23ccb46092e99ae055',\n", " '6380bc23ccb46092e99ae056',\n", " '6380bc23ccb46092e99ae057',\n", " '6380bc23ccb46092e99ae058',\n", " '6380bc23ccb46092e99ae059',\n", " '6380bc23ccb46092e99ae05a',\n", " '6380bc23ccb46092e99ae05b',\n", " '6380bc23ccb46092e99ae05c',\n", " '6380bc23ccb46092e99ae05d',\n", " '6380bc23ccb46092e99ae05e',\n", " '6380bc23ccb46092e99ae05f',\n", " '6380bc23ccb46092e99ae060',\n", " '6380bc23ccb46092e99ae061',\n", " '6380bc23ccb46092e99ae062',\n", " '6380bc23ccb46092e99ae063',\n", " '6380bc23ccb46092e99ae064',\n", " '6380bc23ccb46092e99ae065',\n", " '6380bc23ccb46092e99ae066',\n", " '6380bc23ccb46092e99ae067',\n", " '6380bc23ccb46092e99ae068',\n", " '6380bc23ccb46092e99ae069',\n", " '6380bc23ccb46092e99ae06a',\n", " '6380bc23ccb46092e99ae06b',\n", " '6380bc23ccb46092e99ae06c',\n", " '6380bc23ccb46092e99ae06d',\n", " '6380bc23ccb46092e99ae06e',\n", " '6380bc23ccb46092e99ae06f',\n", " '6380bc23ccb46092e99ae070',\n", " '6380bc23ccb46092e99ae071',\n", " '6380bc23ccb46092e99ae072',\n", " '6380bc23ccb46092e99ae073',\n", " '6380bc23ccb46092e99ae074',\n", " '6380bc23ccb46092e99ae075',\n", " '6380bc23ccb46092e99ae076',\n", " '6380bc23ccb46092e99ae077',\n", " '6380bc23ccb46092e99ae078',\n", " '6380bc23ccb46092e99ae079',\n", " '6380bc23ccb46092e99ae07a',\n", " '6380bc23ccb46092e99ae07b',\n", " '6380bc23ccb46092e99ae07c',\n", " '6380bc23ccb46092e99ae07d',\n", " '6380bc23ccb46092e99ae07e',\n", " '6380bc23ccb46092e99ae07f',\n", " '6380bc23ccb46092e99ae080',\n", " '6380bc23ccb46092e99ae081',\n", " '6380bc23ccb46092e99ae082',\n", " '6380bc23ccb46092e99ae083',\n", " '6380bc23ccb46092e99ae084',\n", " '6380bc23ccb46092e99ae085',\n", " '6380bc23ccb46092e99ae086',\n", " '6380bc23ccb46092e99ae087',\n", " '6380bc23ccb46092e99ae088',\n", " '6380bc23ccb46092e99ae089',\n", " '6380bc23ccb46092e99ae08a',\n", " '6380bc23ccb46092e99ae08b',\n", " '6380bc23ccb46092e99ae08c',\n", " '6380bc23ccb46092e99ae08d',\n", " '6380bc23ccb46092e99ae08e',\n", " '6380bc23ccb46092e99ae08f',\n", " '6380bc23ccb46092e99ae090',\n", " '6380bc23ccb46092e99ae091',\n", " '6380bc23ccb46092e99ae092',\n", " '6380bc23ccb46092e99ae093',\n", " '6380bc23ccb46092e99ae094',\n", " '6380bc23ccb46092e99ae095',\n", " '6380bc23ccb46092e99ae096',\n", " '6380bc23ccb46092e99ae097',\n", " '6380bc23ccb46092e99ae098',\n", " '6380bc23ccb46092e99ae099',\n", " '6380bc23ccb46092e99ae09a',\n", " '6380bc23ccb46092e99ae09b',\n", " '6380bc23ccb46092e99ae09c',\n", " '6380bc23ccb46092e99ae09d',\n", " '6380bc23ccb46092e99ae09e',\n", " '6380bc23ccb46092e99ae09f',\n", " '6380bc23ccb46092e99ae0a0',\n", " '6380bc23ccb46092e99ae0a1',\n", " '6380bc23ccb46092e99ae0a2',\n", " '6380bc23ccb46092e99ae0a3',\n", " '6380bc23ccb46092e99ae0a4',\n", " '6380bc23ccb46092e99ae0a5',\n", " '6380bc23ccb46092e99ae0a6',\n", " '6380bc23ccb46092e99ae0a7',\n", " '6380bc23ccb46092e99ae0a8',\n", " '6380bc23ccb46092e99ae0a9',\n", " '6380bc23ccb46092e99ae0aa',\n", " '6380bc23ccb46092e99ae0ab',\n", " '6380bc23ccb46092e99ae0ac',\n", " '6380bc23ccb46092e99ae0ad',\n", " '6380bc23ccb46092e99ae0ae',\n", " '6380bc23ccb46092e99ae0af',\n", " '6380bc23ccb46092e99ae0b0',\n", " '6380bc23ccb46092e99ae0b1',\n", " '6380bc23ccb46092e99ae0b2',\n", " '6380bc23ccb46092e99ae0b3',\n", " '6380bc23ccb46092e99ae0b4',\n", " '6380bc23ccb46092e99ae0b5',\n", " '6380bc23ccb46092e99ae0b6',\n", " '6380bc23ccb46092e99ae0b7',\n", " '6380bc23ccb46092e99ae0b8',\n", " '6380bc23ccb46092e99ae0b9',\n", " '6380bc23ccb46092e99ae0ba',\n", " '6380bc23ccb46092e99ae0bb',\n", " '6380bc23ccb46092e99ae0bc',\n", " '6380bc23ccb46092e99ae0bd',\n", " '6380bc23ccb46092e99ae13e',\n", " '6380bc23ccb46092e99ae13f',\n", " '6380bc23ccb46092e99ae140',\n", " '6380bc23ccb46092e99ae141',\n", " '6380bc23ccb46092e99ae142',\n", " '6380bc23ccb46092e99ae143',\n", " '6380bc23ccb46092e99ae144',\n", " '6380bc23ccb46092e99ae145',\n", " '6380bc23ccb46092e99ae146',\n", " '6380bc23ccb46092e99ae147',\n", " '6380bc23ccb46092e99ae148',\n", " '6380bc23ccb46092e99ae149',\n", " '6380bc23ccb46092e99ae14a',\n", " '6380bc23ccb46092e99ae14b',\n", " '6380bc23ccb46092e99ae14c',\n", " '6380bc23ccb46092e99ae14d',\n", " '6380bc23ccb46092e99ae14e',\n", " '6380bc23ccb46092e99ae14f',\n", " '6380bc23ccb46092e99ae150',\n", " '6380bc23ccb46092e99ae151',\n", " '6380bc23ccb46092e99ae152',\n", " '6380bc23ccb46092e99ae153',\n", " '6380bc23ccb46092e99ae154',\n", " '6380bc23ccb46092e99ae155',\n", " '6380bc23ccb46092e99ae156',\n", " '6380bc23ccb46092e99ae157',\n", " '6380bc23ccb46092e99ae158',\n", " '6380bc23ccb46092e99ae159',\n", " '6380bc23ccb46092e99ae15a',\n", " '6380bc23ccb46092e99ae15b',\n", " '6380bc23ccb46092e99ae15c',\n", " '6380bc23ccb46092e99ae15d',\n", " '6380bc23ccb46092e99ae15e',\n", " '6380bc23ccb46092e99ae15f',\n", " '6380bc23ccb46092e99ae160',\n", " '6380bc23ccb46092e99ae161',\n", " '6380bc23ccb46092e99ae162',\n", " '6380bc23ccb46092e99ae163',\n", " '6380bc23ccb46092e99ae164',\n", " '6380bc23ccb46092e99ae165',\n", " '6380bc23ccb46092e99ae166',\n", " '6380bc23ccb46092e99ae167',\n", " '6380bc23ccb46092e99ae168',\n", " '6380bc23ccb46092e99ae169',\n", " '6380bc23ccb46092e99ae16a',\n", " '6380bc23ccb46092e99ae16b',\n", " '6380bc23ccb46092e99ae16c',\n", " '6380bc23ccb46092e99ae16d',\n", " '6380bc23ccb46092e99ae16e',\n", " '6380bc23ccb46092e99ae16f',\n", " '6380bc23ccb46092e99ae170',\n", " '6380bc23ccb46092e99ae171',\n", " '6380bc23ccb46092e99ae172',\n", " '6380bc23ccb46092e99ae173',\n", " '6380bc23ccb46092e99ae174',\n", " '6380bc23ccb46092e99ae175',\n", " '6380bc23ccb46092e99ae176',\n", " '6380bc23ccb46092e99ae177',\n", " '6380bc23ccb46092e99ae178',\n", " '6380bc23ccb46092e99ae179',\n", " '6380bc23ccb46092e99ae17a',\n", " '6380bc23ccb46092e99ae17b',\n", " '6380bc23ccb46092e99ae17c',\n", " '6380bc23ccb46092e99ae17d',\n", " '6380bc23ccb46092e99ae17e',\n", " '6380bc23ccb46092e99ae17f',\n", " '6380bc23ccb46092e99ae180',\n", " '6380bc23ccb46092e99ae181',\n", " '6380bc23ccb46092e99ae182',\n", " '6380bc23ccb46092e99ae183',\n", " '6380bc23ccb46092e99ae184',\n", " '6380bc23ccb46092e99ae185',\n", " '6380bc23ccb46092e99ae186',\n", " '6380bc23ccb46092e99ae187',\n", " '6380bc23ccb46092e99ae188',\n", " '6380bc23ccb46092e99ae189',\n", " '6380bc23ccb46092e99ae18a',\n", " '6380bc23ccb46092e99ae18b',\n", " '6380bc23ccb46092e99ae18c',\n", " '6380bc23ccb46092e99ae18d',\n", " '6380bc23ccb46092e99ae18e',\n", " '6380bc23ccb46092e99ae18f',\n", " '6380bc23ccb46092e99ae190',\n", " '6380bc23ccb46092e99ae191',\n", " '6380bc23ccb46092e99ae192',\n", " '6380bc23ccb46092e99ae193',\n", " '6380bc23ccb46092e99ae194',\n", " '6380bc23ccb46092e99ae195',\n", " '6380bc23ccb46092e99ae196',\n", " '6380bc23ccb46092e99ae197',\n", " '6380bc23ccb46092e99ae198',\n", " '6380bc23ccb46092e99ae199',\n", " '6380bc23ccb46092e99ae19a',\n", " '6380bc23ccb46092e99ae19b',\n", " '6380bc23ccb46092e99ae19c',\n", " '6380bc23ccb46092e99ae19d',\n", " '6380bc23ccb46092e99ae19e',\n", " '6380bc23ccb46092e99ae19f',\n", " '6380bc23ccb46092e99ae1a0',\n", " '6380bc23ccb46092e99ae1a1',\n", " '6380bc23ccb46092e99ae1a2',\n", " '6380bc23ccb46092e99ae1a3',\n", " '6380bc23ccb46092e99ae1a4',\n", " '6380bc23ccb46092e99ae1a5',\n", " '6380bc23ccb46092e99ae1a6',\n", " '6380bc23ccb46092e99ae1a7',\n", " '6380bc23ccb46092e99ae1a8',\n", " '6380bc23ccb46092e99ae1a9',\n", " '6380bc23ccb46092e99ae1aa',\n", " '6380bc23ccb46092e99ae1ab',\n", " '6380bc23ccb46092e99ae1ac',\n", " '6380bc23ccb46092e99ae1ad',\n", " '6380bc23ccb46092e99ae1ae',\n", " '6380bc23ccb46092e99ae1af',\n", " '6380bc23ccb46092e99ae1b0',\n", " '6380bc23ccb46092e99ae1b1',\n", " '6380bc23ccb46092e99ae1b2',\n", " '6380bc23ccb46092e99ae1b3',\n", " '6380bc23ccb46092e99ae1b4',\n", " '6380bc23ccb46092e99ae1b5',\n", " '6380bc23ccb46092e99ae1b6',\n", " '6380bc23ccb46092e99ae1b7',\n", " '6380bc23ccb46092e99ae1b8',\n", " '6380bc23ccb46092e99ae1b9',\n", " '6380bc23ccb46092e99ae1ba',\n", " '6380bc23ccb46092e99ae1bb',\n", " '6380bc23ccb46092e99ae1bc',\n", " '6380bc23ccb46092e99ae1bd',\n", " '6380bc23ccb46092e99ae1be',\n", " '6380bc23ccb46092e99ae1bf',\n", " '6380bc23ccb46092e99ae1c0',\n", " '6380bc23ccb46092e99ae1c1',\n", " '6380bc23ccb46092e99ae1c2',\n", " '6380bc23ccb46092e99ae1c3',\n", " '6380bc23ccb46092e99ae1c4',\n", " '6380bc23ccb46092e99ae1c5',\n", " '6380bc23ccb46092e99ae1c6',\n", " '6380bc23ccb46092e99ae1c7',\n", " '6380bc23ccb46092e99ae1c8',\n", " '6380bc23ccb46092e99ae1c9',\n", " '6380bc23ccb46092e99ae1ca',\n", " '6380bc23ccb46092e99ae1cb',\n", " '6380bc23ccb46092e99ae1cc',\n", " '6380bc23ccb46092e99ae1cd',\n", " '6380bc23ccb46092e99ae1ce',\n", " '6380bc23ccb46092e99ae1cf',\n", " '6380bc23ccb46092e99ae1d0',\n", " '6380bc23ccb46092e99ae1d1',\n", " '6380bc23ccb46092e99ae1d2',\n", " '6380bc23ccb46092e99ae1d3',\n", " '6380bc23ccb46092e99ae1d4',\n", " '6380bc23ccb46092e99ae1d5',\n", " '6380bc23ccb46092e99ae1d6',\n", " '6380bc23ccb46092e99ae1d7',\n", " '6380bc23ccb46092e99ae1d8',\n", " '6380bc23ccb46092e99ae1d9',\n", " '6380bc23ccb46092e99ae1da',\n", " '6380bc23ccb46092e99ae1db',\n", " '6380bc23ccb46092e99ae1dc',\n", " '6380bc23ccb46092e99ae1dd',\n", " '6380bc23ccb46092e99ae1de',\n", " '6380bc23ccb46092e99ae1df',\n", " '6380bc23ccb46092e99ae1e0',\n", " '6380bc23ccb46092e99ae1e1',\n", " '6380bc23ccb46092e99ae1e2',\n", " '6380bc23ccb46092e99ae1e3',\n", " '6380bc23ccb46092e99ae1e4',\n", " '6380bc23ccb46092e99ae1e5',\n", " '6380bc23ccb46092e99ae1e6',\n", " '6380bc23ccb46092e99ae1e7',\n", " '6380bc23ccb46092e99ae1e8',\n", " '6380bc23ccb46092e99ae1e9',\n", " '6380bc23ccb46092e99ae1ea',\n", " '6380bc23ccb46092e99ae1eb',\n", " '6380bc23ccb46092e99ae1ec',\n", " '6380bc23ccb46092e99ae1ed',\n", " '6380bc23ccb46092e99ae1ee',\n", " '6380bc23ccb46092e99ae1ef',\n", " '6380bc23ccb46092e99ae1f0',\n", " '6380bc23ccb46092e99ae1f1',\n", " '6380bc23ccb46092e99ae1f2',\n", " '6380bc23ccb46092e99ae1f3',\n", " '6380bc23ccb46092e99ae1f4',\n", " '6380bc23ccb46092e99ae1f5',\n", " '6380bc23ccb46092e99ae1f6',\n", " '6380bc23ccb46092e99ae1f7',\n", " '6380bc23ccb46092e99ae1f8',\n", " '6380bc23ccb46092e99ae1f9',\n", " '6380bc23ccb46092e99ae1fa',\n", " '6380bc23ccb46092e99ae1fb',\n", " '6380bc23ccb46092e99ae1fc',\n", " '6380bc23ccb46092e99ae1fd',\n", " '6380bc23ccb46092e99ae1fe',\n", " '6380bc23ccb46092e99ae1ff',\n", " '6380bc23ccb46092e99ae200',\n", " '6380bc23ccb46092e99ae201',\n", " '6380bc23ccb46092e99ae202',\n", " '6380bc23ccb46092e99ae203',\n", " '6380bc23ccb46092e99ae204',\n", " '6380bc23ccb46092e99ae205',\n", " '6380bc23ccb46092e99ae206',\n", " '6380bc23ccb46092e99ae207',\n", " '6380bc23ccb46092e99ae208',\n", " '6380bc23ccb46092e99ae209',\n", " '6380bc23ccb46092e99ae20a',\n", " '6380bc23ccb46092e99ae20b',\n", " '6380bc23ccb46092e99ae20c',\n", " '6380bc23ccb46092e99ae20d',\n", " '6380bc23ccb46092e99ae2de',\n", " '6380bc23ccb46092e99ae2df',\n", " '6380bc23ccb46092e99ae2e0',\n", " '6380bc23ccb46092e99ae2e1',\n", " '6380bc23ccb46092e99ae2e2',\n", " '6380bc23ccb46092e99ae2e3',\n", " '6380bc23ccb46092e99ae2e4',\n", " '6380bc23ccb46092e99ae2e5',\n", " '6380bc23ccb46092e99ae2e6',\n", " '6380bc23ccb46092e99ae2e7',\n", " '6380bc23ccb46092e99ae2e8',\n", " '6380bc23ccb46092e99ae2e9',\n", " '6380bc23ccb46092e99ae2ea',\n", " '6380bc23ccb46092e99ae2eb',\n", " '6380bc23ccb46092e99ae2ec',\n", " '6380bc23ccb46092e99ae2ed',\n", " '6380bc23ccb46092e99ae2ee',\n", " '6380bc23ccb46092e99ae2ef',\n", " '6380bc23ccb46092e99ae2f0',\n", " '6380bc23ccb46092e99ae2f1',\n", " '6380bc23ccb46092e99ae2f2',\n", " '6380bc23ccb46092e99ae2f3',\n", " '6380bc23ccb46092e99ae2f4',\n", " '6380bc23ccb46092e99ae2f5',\n", " '6380bc23ccb46092e99ae2f6',\n", " '6380bc23ccb46092e99ae2f7',\n", " '6380bc23ccb46092e99ae2f8',\n", " '6380bc23ccb46092e99ae2f9',\n", " '6380bc23ccb46092e99ae2fa',\n", " '6380bc23ccb46092e99ae2fb',\n", " '6380bc23ccb46092e99ae2fc',\n", " '6380bc23ccb46092e99ae2fd',\n", " '6380bc23ccb46092e99ae2fe',\n", " '6380bc23ccb46092e99ae2ff',\n", " '6380bc23ccb46092e99ae300',\n", " '6380bc23ccb46092e99ae301',\n", " '6380bc23ccb46092e99ae302',\n", " '6380bc23ccb46092e99ae303',\n", " '6380bc23ccb46092e99ae304',\n", " '6380bc23ccb46092e99ae305',\n", " '6380bc23ccb46092e99ae306',\n", " '6380bc23ccb46092e99ae307',\n", " '6380bc23ccb46092e99ae308',\n", " '6380bc23ccb46092e99ae309',\n", " '6380bc23ccb46092e99ae30a',\n", " '6380bc23ccb46092e99ae30b',\n", " '6380bc23ccb46092e99ae30c',\n", " '6380bc23ccb46092e99ae30d',\n", " '6380bc23ccb46092e99ae30e',\n", " '6380bc23ccb46092e99ae30f',\n", " '6380bc23ccb46092e99ae310',\n", " '6380bc23ccb46092e99ae311',\n", " '6380bc23ccb46092e99ae312',\n", " '6380bc23ccb46092e99ae313',\n", " '6380bc23ccb46092e99ae314',\n", " '6380bc23ccb46092e99ae315',\n", " '6380bc23ccb46092e99ae316',\n", " '6380bc23ccb46092e99ae317',\n", " '6380bc23ccb46092e99ae318',\n", " '6380bc23ccb46092e99ae319',\n", " '6380bc23ccb46092e99ae31a',\n", " '6380bc23ccb46092e99ae31b',\n", " '6380bc23ccb46092e99ae31c',\n", " '6380bc23ccb46092e99ae31d',\n", " '6380bc23ccb46092e99ae31e',\n", " '6380bc23ccb46092e99ae31f',\n", " '6380bc23ccb46092e99ae320',\n", " '6380bc23ccb46092e99ae321',\n", " '6380bc23ccb46092e99ae322',\n", " '6380bc23ccb46092e99ae323',\n", " '6380bc23ccb46092e99ae324',\n", " '6380bc23ccb46092e99ae325',\n", " '6380bc23ccb46092e99ae326',\n", " '6380bc23ccb46092e99ae327',\n", " '6380bc23ccb46092e99ae328',\n", " '6380bc23ccb46092e99ae329',\n", " '6380bc23ccb46092e99ae32a',\n", " '6380bc23ccb46092e99ae32b',\n", " '6380bc23ccb46092e99ae32c',\n", " '6380bc23ccb46092e99ae32d',\n", " '6380bc23ccb46092e99ae32e',\n", " '6380bc23ccb46092e99ae32f',\n", " '6380bc23ccb46092e99ae330',\n", " '6380bc23ccb46092e99ae331',\n", " '6380bc23ccb46092e99ae332',\n", " '6380bc23ccb46092e99ae333',\n", " '6380bc23ccb46092e99ae334',\n", " '6380bc23ccb46092e99ae335',\n", " '6380bc23ccb46092e99ae336',\n", " '6380bc23ccb46092e99ae337',\n", " '6380bc23ccb46092e99ae338',\n", " '6380bc23ccb46092e99ae339',\n", " '6380bc23ccb46092e99ae33a',\n", " '6380bc23ccb46092e99ae33b',\n", " '6380bc23ccb46092e99ae33c',\n", " '6380bc23ccb46092e99ae33d',\n", " '6380bc23ccb46092e99ae33e',\n", " '6380bc23ccb46092e99ae33f',\n", " '6380bc23ccb46092e99ae340',\n", " '6380bc23ccb46092e99ae341',\n", " '6380bc23ccb46092e99ae342',\n", " '6380bc23ccb46092e99ae343',\n", " '6380bc23ccb46092e99ae344',\n", " '6380bc23ccb46092e99ae345',\n", " '6380bc23ccb46092e99ae346',\n", " '6380bc23ccb46092e99ae347',\n", " '6380bc23ccb46092e99ae348',\n", " '6380bc23ccb46092e99ae349',\n", " '6380bc23ccb46092e99ae34a',\n", " '6380bc23ccb46092e99ae34b',\n", " '6380bc23ccb46092e99ae34c',\n", " '6380bc23ccb46092e99ae34d',\n", " '6380bc23ccb46092e99ae34e',\n", " '6380bc23ccb46092e99ae34f',\n", " '6380bc23ccb46092e99ae350',\n", " '6380bc23ccb46092e99ae351',\n", " '6380bc23ccb46092e99ae352',\n", " '6380bc23ccb46092e99ae353',\n", " '6380bc23ccb46092e99ae354',\n", " '6380bc23ccb46092e99ae355',\n", " '6380bc23ccb46092e99ae356',\n", " '6380bc23ccb46092e99ae357',\n", " '6380bc23ccb46092e99ae358',\n", " '6380bc23ccb46092e99ae359',\n", " '6380bc23ccb46092e99ae35a',\n", " '6380bc23ccb46092e99ae35b',\n", " '6380bc23ccb46092e99ae35c',\n", " '6380bc23ccb46092e99ae35d',\n", " '6380bc23ccb46092e99ae35e',\n", " '6380bc23ccb46092e99ae35f',\n", " '6380bc23ccb46092e99ae360',\n", " '6380bc23ccb46092e99ae361',\n", " '6380bc23ccb46092e99ae362',\n", " '6380bc23ccb46092e99ae363',\n", " '6380bc23ccb46092e99ae364',\n", " '6380bc23ccb46092e99ae365',\n", " '6380bc23ccb46092e99ae366',\n", " '6380bc23ccb46092e99ae367',\n", " '6380bc23ccb46092e99ae368',\n", " '6380bc23ccb46092e99ae369',\n", " '6380bc23ccb46092e99ae36a',\n", " '6380bc23ccb46092e99ae36b',\n", " '6380bc23ccb46092e99ae36c',\n", " '6380bc23ccb46092e99ae36d',\n", " '6380bc23ccb46092e99ae36e',\n", " '6380bc23ccb46092e99ae36f',\n", " '6380bc23ccb46092e99ae370',\n", " '6380bc23ccb46092e99ae371',\n", " '6380bc23ccb46092e99ae372',\n", " '6380bc23ccb46092e99ae373',\n", " '6380bc23ccb46092e99ae374',\n", " '6380bc23ccb46092e99ae375',\n", " '6380bc23ccb46092e99ae376',\n", " '6380bc23ccb46092e99ae377',\n", " '6380bc23ccb46092e99ae378',\n", " '6380bc23ccb46092e99ae414',\n", " '6380bc23ccb46092e99ae415',\n", " '6380bc23ccb46092e99ae416',\n", " '6380bc23ccb46092e99ae417',\n", " '6380bc23ccb46092e99ae418',\n", " '6380bc23ccb46092e99ae419',\n", " '6380bc23ccb46092e99ae41a',\n", " '6380bc23ccb46092e99ae41b',\n", " '6380bc23ccb46092e99ae41c',\n", " '6380bc23ccb46092e99ae41d',\n", " '6380bc23ccb46092e99ae41e',\n", " '6380bc23ccb46092e99ae41f',\n", " '6380bc23ccb46092e99ae420',\n", " '6380bc23ccb46092e99ae421',\n", " '6380bc23ccb46092e99ae422',\n", " '6380bc23ccb46092e99ae423',\n", " '6380bc23ccb46092e99ae424',\n", " '6380bc23ccb46092e99ae425',\n", " '6380bc23ccb46092e99ae426',\n", " '6380bc23ccb46092e99ae427',\n", " '6380bc23ccb46092e99ae428',\n", " '6380bc23ccb46092e99ae429',\n", " '6380bc23ccb46092e99ae42a',\n", " '6380bc23ccb46092e99ae42b',\n", " '6380bc23ccb46092e99ae42c',\n", " '6380bc23ccb46092e99ae42d',\n", " '6380bc23ccb46092e99ae42e',\n", " '6380bc23ccb46092e99ae42f',\n", " '6380bc23ccb46092e99ae430',\n", " '6380bc23ccb46092e99ae431',\n", " '6380bc23ccb46092e99ae432',\n", " '6380bc23ccb46092e99ae433',\n", " '6380bc23ccb46092e99ae434',\n", " '6380bc23ccb46092e99ae435',\n", " '6380bc23ccb46092e99ae436',\n", " '6380bc23ccb46092e99ae437',\n", " '6380bc23ccb46092e99ae438',\n", " '6380bc23ccb46092e99ae439',\n", " '6380bc23ccb46092e99ae43a',\n", " '6380bc23ccb46092e99ae43b',\n", " '6380bc23ccb46092e99ae43c',\n", " '6380bc23ccb46092e99ae43d',\n", " '6380bc23ccb46092e99ae43e',\n", " '6380bc23ccb46092e99ae43f',\n", " '6380bc23ccb46092e99ae440',\n", " '6380bc23ccb46092e99ae441',\n", " '6380bc23ccb46092e99ae442',\n", " '6380bc23ccb46092e99ae443',\n", " '6380bc23ccb46092e99ae444',\n", " '6380bc23ccb46092e99ae445',\n", " '6380bc23ccb46092e99ae446',\n", " '6380bc23ccb46092e99ae447',\n", " '6380bc23ccb46092e99ae448',\n", " '6380bc23ccb46092e99ae449',\n", " '6380bc23ccb46092e99ae44a',\n", " '6380bc23ccb46092e99ae44b',\n", " '6380bc23ccb46092e99ae44c',\n", " '6380bc23ccb46092e99ae44d',\n", " '6380bc23ccb46092e99ae44e',\n", " '6380bc23ccb46092e99ae44f',\n", " '6380bc23ccb46092e99ae450',\n", " '6380bc23ccb46092e99ae451',\n", " '6380bc23ccb46092e99ae452',\n", " '6380bc23ccb46092e99ae453',\n", " '6380bc23ccb46092e99ae454',\n", " '6380bc23ccb46092e99ae455',\n", " '6380bc23ccb46092e99ae456',\n", " '6380bc23ccb46092e99ae457',\n", " '6380bc23ccb46092e99ae458',\n", " '6380bc23ccb46092e99ae459',\n", " '6380bc23ccb46092e99ae45a',\n", " '6380bc23ccb46092e99ae45b',\n", " '6380bc23ccb46092e99ae45c',\n", " '6380bc23ccb46092e99ae45d',\n", " '6380bc23ccb46092e99ae45e',\n", " '6380bc23ccb46092e99ae45f',\n", " '6380bc23ccb46092e99ae460',\n", " '6380bc23ccb46092e99ae461',\n", " '6380bc23ccb46092e99ae462',\n", " '6380bc23ccb46092e99ae463',\n", " '6380bc23ccb46092e99ae464',\n", " '6380bc23ccb46092e99ae465',\n", " '6380bc23ccb46092e99ae466',\n", " '6380bc23ccb46092e99ae467',\n", " '6380bc23ccb46092e99ae468',\n", " '6380bc23ccb46092e99ae469',\n", " '6380bc23ccb46092e99ae46a',\n", " '6380bc23ccb46092e99ae46b',\n", " '6380bc23ccb46092e99ae46c',\n", " '6380bc23ccb46092e99ae46d',\n", " '6380bc23ccb46092e99ae46e',\n", " '6380bc23ccb46092e99ae46f',\n", " '6380bc23ccb46092e99ae470',\n", " '6380bc23ccb46092e99ae471',\n", " '6380bc23ccb46092e99ae472',\n", " '6380bc23ccb46092e99ae473',\n", " '6380bc23ccb46092e99ae474',\n", " '6380bc23ccb46092e99ae475',\n", " '6380bc23ccb46092e99ae476',\n", " '6380bc23ccb46092e99ae477',\n", " '6380bc23ccb46092e99ae478',\n", " '6380bc23ccb46092e99ae479',\n", " '6380bc23ccb46092e99ae47a',\n", " '6380bc23ccb46092e99ae47b',\n", " '6380bc23ccb46092e99ae47c',\n", " '6380bc23ccb46092e99ae47d',\n", " '6380bc23ccb46092e99ae47e',\n", " '6380bc23ccb46092e99ae47f',\n", " '6380bc23ccb46092e99ae480',\n", " '6380bc23ccb46092e99ae481',\n", " '6380bc23ccb46092e99ae482',\n", " '6380bc23ccb46092e99ae483',\n", " '6380bc23ccb46092e99ae484',\n", " '6380bc23ccb46092e99ae485',\n", " '6380bc23ccb46092e99ae486',\n", " '6380bc23ccb46092e99ae487',\n", " '6380bc23ccb46092e99ae488',\n", " '6380bc23ccb46092e99ae489',\n", " '6380bc23ccb46092e99ae48a',\n", " '6380bc23ccb46092e99ae48b',\n", " '6380bc23ccb46092e99ae48c',\n", " '6380bc23ccb46092e99ae48d',\n", " '6380bc23ccb46092e99ae48e',\n", " '6380bc23ccb46092e99ae48f',\n", " '6380bc23ccb46092e99ae490',\n", " '6380bc23ccb46092e99ae491',\n", " '6380bc23ccb46092e99ae492',\n", " '6380bc23ccb46092e99ae493',\n", " '6380bc23ccb46092e99ae494',\n", " '6380bc23ccb46092e99ae495',\n", " '6380bc23ccb46092e99ae496',\n", " '6380bc23ccb46092e99ae497',\n", " '6380bc23ccb46092e99ae498',\n", " '6380bc23ccb46092e99ae499',\n", " '6380bc23ccb46092e99ae49a',\n", " '6380bc23ccb46092e99ae49b',\n", " '6380bc23ccb46092e99ae49c',\n", " '6380bc23ccb46092e99ae49d',\n", " '6380bc23ccb46092e99ae49e',\n", " '6380bc23ccb46092e99ae49f',\n", " '6380bc23ccb46092e99ae4a0',\n", " '6380bc23ccb46092e99ae4a1',\n", " '6380bc23ccb46092e99ae4a2',\n", " '6380bc23ccb46092e99ae4a3',\n", " '6380bc23ccb46092e99ae4a4',\n", " '6380bc23ccb46092e99ae4a5',\n", " '6380bc23ccb46092e99ae4a6',\n", " '6380bc23ccb46092e99ae4a7',\n", " '6380bc23ccb46092e99ae4a8',\n", " '6380bc23ccb46092e99ae4a9',\n", " '6380bc23ccb46092e99ae4aa',\n", " '6380bc23ccb46092e99ae4ab',\n", " '6380bc23ccb46092e99ae4ac',\n", " '6380bc23ccb46092e99ae4ad',\n", " '6380bc23ccb46092e99ae4ae',\n", " '6380bc23ccb46092e99ae4af',\n", " '6380bc23ccb46092e99ae4b0',\n", " '6380bc23ccb46092e99ae4b1',\n", " '6380bc23ccb46092e99ae4b2',\n", " '6380bc23ccb46092e99ae4b3',\n", " '6380bc23ccb46092e99ae4b4',\n", " '6380bc23ccb46092e99ae4b5',\n", " '6380bc23ccb46092e99ae4b6',\n", " '6380bc23ccb46092e99ae4b7',\n", " '6380bc23ccb46092e99ae4b8',\n", " '6380bc23ccb46092e99ae4b9',\n", " '6380bc23ccb46092e99ae4ba',\n", " '6380bc23ccb46092e99ae4bb',\n", " '6380bc23ccb46092e99ae4bc',\n", " '6380bc24ccb46092e99ae566',\n", " '6380bc24ccb46092e99ae567',\n", " '6380bc24ccb46092e99ae568',\n", " '6380bc24ccb46092e99ae569',\n", " '6380bc24ccb46092e99ae56a',\n", " '6380bc24ccb46092e99ae56b',\n", " '6380bc24ccb46092e99ae56c',\n", " '6380bc24ccb46092e99ae56d',\n", " '6380bc24ccb46092e99ae56e',\n", " '6380bc24ccb46092e99ae56f',\n", " '6380bc24ccb46092e99ae570',\n", " '6380bc24ccb46092e99ae571',\n", " '6380bc24ccb46092e99ae572',\n", " '6380bc24ccb46092e99ae573',\n", " '6380bc24ccb46092e99ae574',\n", " '6380bc24ccb46092e99ae575',\n", " '6380bc24ccb46092e99ae576',\n", " '6380bc24ccb46092e99ae577',\n", " '6380bc24ccb46092e99ae578',\n", " '6380bc24ccb46092e99ae579',\n", " '6380bc24ccb46092e99ae57a',\n", " ...]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset = fo.Dataset(\"fish-classification-184\")\n", "dataset.add_samples(samples)\n", "dataset.persistent = True\n", "dataset.save()"]}], "metadata": {"kernelspec": {"display_name": "detectron2_env", "language": "python", "name": "detectron2_env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.13"}}, "nbformat": 4, "nbformat_minor": 5}