{"cells": [{"cell_type": "code", "execution_count": 4, "id": "17f28a7d", "metadata": {}, "outputs": [], "source": ["import os\n", "import cv2\n", "import sys\n", "#Change path specificly to your directories\n", "sys.path.insert(1, '/home/<USER>/Fishial/FishialReaserch')\n", "import copy\n", "import time\n", "import json\n", "import torch\n", "import numpy as np\n", "import pandas as pd\n", "import torchvision.models as models\n", "import matplotlib.pyplot as plt\n", "\n", "from torch import nn\n", "from torchvision import transforms\n", "\n", "\n", "from os import listdir\n", "from os.path import isfile, join\n", "\n", "from module.segmentation_package.interpreter_segm import SegmentationInference\n", "from module.classification_package.src.dataset import FishialDataset\n", "from module.classification_package.src.model import EmbeddingModel, Backbone, Model, FcNet\n", "\n", "from PIL import Image\n", "from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay\n", "\n", "\n", "pd.options.display.max_rows = 999"]}, {"cell_type": "code", "execution_count": 5, "id": "741c1760", "metadata": {}, "outputs": [], "source": ["def _get_scores(report):\n", "    labels_row = []\n", "    scores = []\n", "    for i in report:\n", "        labels_row.append(i)\n", "        row = []\n", "        if type(report[i]) != dict:\n", "            continue\n", "        for z in report[i]:\n", "            row.append(report[i][z])\n", "        scores.append(row)\n", "    scores = np.array(scores[:61])\n", "    \n", "    return labels_row, scores\n", "\n", "def init_model_class(ckp=None):\n", "    resnet18 = models.resnet18(pretrained=True)\n", "    resnet18.fc = nn.Identity()\n", "\n", "    backbone = Backbone(resnet18)\n", "    model = FcNet(backbone, 61)\n", "    if ckp:\n", "        model.load_state_dict(torch.load(ckp))\n", "    model.eval()\n", "    return model\n", "\n", "def init_model_embed(ckp=None):\n", "    resnet18 = models.resnet18(pretrained=True)\n", "    resnet18.fc = nn.Identity()\n", "\n", "    backbone = Backbone(resnet18)\n", "    model = EmbeddingModel(backbone)\n", "    if ckp:\n", "        model.load_state_dict(torch.load(ckp))\n", "    model.eval()\n", "    return model\n", "\n", "def remove_dupliceta(mylist):\n", "    seen = set()\n", "    newlist = []\n", "    for item in mylist:\n", "        t = tuple(item)\n", "        if t not in seen:\n", "            newlist.append(item)\n", "            seen.add(t)\n", "    return newlist\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "id": "96b54fd8", "metadata": {}, "outputs": [], "source": ["loader = transforms.Compose([\n", "            transforms.<PERSON><PERSON><PERSON>([224, 224]),\n", "            transforms.To<PERSON><PERSON><PERSON>(),\n", "            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])\n", "        ])\n", "data_set_train = FishialDataset(\n", "        json_path=\"data_train.json\",\n", "        root_folder=\"/home/<USER>/Fishial/FishialReaserch/datasets/cutted_fish\",\n", "        transform=loader\n", "    )\n", "\n", "# data_set_val = FishialDataset(\n", "#         json_path=\"data_test.json\",\n", "#         root_folder=\"/home/<USER>/Fishial/FishialReaserch/dataset/\",\n", "#         transform=loader\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "id": "65e84c4e", "metadata": {}, "outputs": [], "source": ["list_of_reports = ['output/cross_entropy_best.json','output/triplet_best.json']"]}, {"cell_type": "code", "execution_count": null, "id": "17166b65", "metadata": {}, "outputs": [], "source": ["labels = []\n", "scores = []\n", "for report in list_of_reports:\n", "    basename = os.path.basename(report).split('.')[0]\n", "    data_r = read_json(report)\n", "    \n", "    labels, scores_tmp = _get_scores(data_r)\n", "    if len(scores) == 0:\n", "        scores = scores_tmp\n", "    else:\n", "        scores = np.concatenate((scores, scores_tmp), axis=1)\n", "        \n", "    full_data = {}\n", "    for model_score in range(int(len(scores[0])/4)):\n", "        for idxx, name in enumerate(['Pre', 'TPR', 'F1']):\n", "            name_metrics = \"{}_{}\".format(name, model_score)\n", "            full_data.update({name_metrics: [round(iix, 2) for iix in scores[:, model_score * 4 + idxx]]})\n", "        \n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "cbb369d3", "metadata": {}, "outputs": [], "source": ["full_data = {}\n", "for model_score in range(int(len(scores[0])/4)):\n", "    for idxx, name in enumerate(['Pre', 'TPR', 'F1']):\n", "        name_metrics = \"{}_{}\".format(name, model_score)\n", "        full_data.update({name_metrics: [round(iix, 2) for iix in scores[:, model_score * 4 + idxx]]})\n", "df=pd.DataFrame(full_data)\n", "df.index = labels[:61]\n", "for column in range(int(len(df.columns)/3) - 1):\n", "    for metric_n in range(3):\n", "        name = df.columns[3 * (column+1) + metric_n]\n", "        main_column = name[:len(name)-1]+\"0\"\n", "        df[name] = df[name] - df[main_column]"]}, {"cell_type": "code", "execution_count": null, "id": "6a9c03ca", "metadata": {}, "outputs": [], "source": ["print(sum(df['Pre_1']), sum(df['TPR_1']), sum(df['F1_1']))"]}, {"cell_type": "code", "execution_count": null, "id": "2d277b06", "metadata": {}, "outputs": [], "source": ["model_class = init_model_class('output/ckpt_adam_cross_entropy_0.837245696400626_41800.0.ckpt')\n", "model_embed = init_model_embed('output/ckpt_triplet_cross_entropy_0.87_42000.0.ckpt')\n", "\n", "data_train = get_data_base(model_embed, data_set_train)\n", "data_eval = get_data_base(model_embed, data_set_val)"]}, {"cell_type": "code", "execution_count": null, "id": "e4195058", "metadata": {}, "outputs": [], "source": ["lables = [data_set_train.library_name[xcx]['label'] for xcx in data_set_train.library_name][:61]"]}, {"cell_type": "code", "execution_count": null, "id": "79de25bc", "metadata": {"scrolled": true}, "outputs": [], "source": ["segm_class = SegmentationInference(model_path = '../../best_scores/model_0067499_amp_on.pth')\n", "softmax = nn.Softmax(dim=None)\n", "\n", "y_true = []\n", "y_embed_61_pred = []\n", "y_class_pred = []\n", "\n", "dir_valid = '../self_cuted'\n", "\n", "dirs = [os.path.join(dir_valid, o) for o in os.listdir(dir_valid) \n", "                    if os.path.isdir(os.path.join(dir_valid,o))]\n", "\n", "for indiece, specie_dir in enumerate(dirs):\n", "    print(\"Left: {}\".format(len(dirs) - indiece), end='\\r' )\n", "    \n", "    basename = os.path.basename(specie_dir)\n", "    if basename not in labels:\n", "        print(basename)\n", "        \n", "    imgs = [f for f in listdir(specie_dir) if isfile(join(specie_dir, f))]\n", "\n", "    for img_name in imgs:\n", "        path_img = os.path.join(specie_dir, img_name)\n", "        try:\n", "            mask = cv2.imread(path_img)\n", "            mask = cv2.cvtColor(mask, cv2.COLOR_BGR2RGB)\n", "        except:\n", "            continue\n", "            \n", "        image = Image.fromarray(mask)\n", "        image = loader(image).float()\n", "        image = torch.tensor(image)\n", "\n", "        dump_embed = model_embed(image.unsqueeze(0)).detach().numpy()\n", "        topest = classify(data_train, dump_embed)\n", "        flatten = [iii[0] for iii in topest[:10]]\n", "        my_dict = [[i, flatten.count(i)] for i in flatten]\n", "        my_dict = remove_dupliceta(my_dict)\n", "        my_dict = sorted(my_dict, key=lambda x: x[1], reverse=True)\n", "\n", "        dump = softmax(model_class(image.unsqueeze(0)))\n", "        output = torch.topk(dump, 3)\n", "        y_true.append(basename)\n", "        y_embed_61_pred.append(labels[topest[0][0]])\n", "        y_class_pred.append(lables[int(output.indices[0][0])])"]}, {"cell_type": "code", "execution_count": null, "id": "14e4b3ec", "metadata": {"scrolled": false}, "outputs": [], "source": ["labels = [data_set_train.library_name[i]['label'] for i in data_set_train.library_name]\n", "cm = confusion_matrix(y_true, y_embed_61_pred, normalize='true')\n", "fig, ax = plt.subplots(figsize=(30, 30))\n", "disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=labels)\n", "disp = disp.plot(cmap=plt.cm.Blues, ax=ax, xticks_rotation=90)\n", "plt.show()\n", "print(classification_report(y_true, y_embed_61_pred, target_names=labels))"]}, {"cell_type": "code", "execution_count": null, "id": "9949fe88", "metadata": {}, "outputs": [], "source": ["labels = [data_set_train.library_name[i]['label'] for i in data_set_train.library_name]\n", "cm = confusion_matrix(y_true, y_class_pred, normalize='true')\n", "fig, ax = plt.subplots(figsize=(30, 30))\n", "disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=labels)\n", "disp = disp.plot(cmap=plt.cm.Blues, ax=ax, xticks_rotation=90)\n", "plt.show()\n", "print(classification_report(y_true, y_class_pred, target_names=labels))"]}, {"cell_type": "markdown", "id": "1ca6c806", "metadata": {}, "source": ["The script bellow make a cuted fishs from src dir to distanation by mask rcnn net"]}, {"cell_type": "code", "execution_count": null, "id": "b2c64210", "metadata": {}, "outputs": [], "source": ["segm_class = SegmentationInference(model_path = '../../best_scores/model_0067499_amp_on.pth')\n", "\n", "src_dir = '../self_validation'\n", "new_dir = '../self_cuted'\n", "dirs = [os.path.join(src_dir, o) for o in os.listdir(src_dir) \n", "                    if os.path.isdir(os.path.join(src_dir,o))]\n", "for path in dirs:\n", "    print(path)\n", "    basename = os.path.basename(path)\n", "    new_spieces_dir_path = os.path.join(new_dir, basename)\n", "    \n", "    os.makedirs(new_spieces_dir_path, exist_ok=True)\n", "    imgs = [f for f in listdir(path) if isfile(join(path, f))]\n", "    numss = 0\n", "    for img_name in imgs:\n", "        path_img = os.path.join(os.path.join(src_dir, basename), img_name)\n", "        try:\n", "            img = cv2.imread(path_img)\n", "            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n", "        except:\n", "            print(\"Error: \", path_img)\n", "            continue\n", "        masks = segm_class.simple_inference(img, output='mask')\n", "        for mask in masks:\n", "            numss += 1\n", "            mask_path = os.path.join(new_spieces_dir_path, \"{}_img_{}.png\".format(basename, numss))\n", "            cv2.imwrite(mask_path, cv2.cvtColor(mask, cv2.COLOR_RGB2BGR)) \n", "    print(\"Total: \", numss)"]}, {"cell_type": "code", "execution_count": 13, "id": "8f2008b4", "metadata": {}, "outputs": [], "source": ["test = {0: {'num': '336', 'label': 'Pomatomus saltatrix'},\n", " 1: {'num': '157', 'label': 'Acanthocybium solandri'},\n", " 2: {'num': '94', 'label': 'Carassius auratus'},\n", " 3: {'num': '210', 'label': 'Caranx hippos'},\n", " 4: {'num': '604', 'label': 'Thunnus atlanticus'},\n", " 5: {'num': '436', 'label': 'Perca flavescens'},\n", " 6: {'num': '20', 'label': '<PERSON><PERSON><PERSON> fontinalis'},\n", " 7: {'num': '18', 'label': 'Oncorhynchus mykiss'},\n", " 8: {'num': '383', 'label': 'Esox lucius'},\n", " 9: {'num': '11', 'label': 'Pterois volitans'},\n", " 10: {'num': '10', 'label': '<PERSON><PERSON><PERSON>na barracuda'},\n", " 11: {'num': '529', 'label': 'Lepomis macrochirus'},\n", " 12: {'num': '703', 'label': 'Pogonias cromis'},\n", " 13: {'num': '248', 'label': '<PERSON><PERSON><PERSON><PERSON> ocellatus'},\n", " 14: {'num': '416', 'label': '<PERSON><PERSON><PERSON> gulosus'},\n", " 15: {'num': '684', 'label': 'Esox masquinongy'},\n", " 16: {'num': '23', 'label': 'Salmo trutta'},\n", " 17: {'num': '15', 'label': 'Scomberomorus cavalla'},\n", " 18: {'num': '13', 'label': '<PERSON>pt<PERSON> salmoides'},\n", " 19: {'num': '5', 'label': '<PERSON><PERSON><PERSON> hippurus'},\n", " 20: {'num': '22', 'label': 'Micropt<PERSON> dolomieu'},\n", " 21: {'num': '91', 'label': 'Balistes capriscus'},\n", " 22: {'num': '16', 'label': 'Thunnus albacares'},\n", " 23: {'num': '247', 'label': 'Megalops atlanticus'},\n", " 24: {'num': '235', 'label': '<PERSON><PERSON><PERSON><PERSON> falcatus'},\n", " 25: {'num': '14', 'label': '<PERSON><PERSON> saxatilis'},\n", " 26: {'num': '237', 'label': '<PERSON><PERSON><PERSON> du<PERSON>'},\n", " 27: {'num': '356', 'label': 'Caranx crysos'},\n", " 28: {'num': '658', 'label': 'Lu<PERSON>janus vivanus'},\n", " 29: {'num': '234', 'label': 'Scomb<PERSON>morus maculatus'},\n", " 30: {'num': '162', 'label': 'Rachycentron canadum'},\n", " 31: {'num': '29', 'label': 'Amphiprion percula'},\n", " 32: {'num': '252', 'label': 'Haemulon sciurus'},\n", " 33: {'num': '347', 'label': 'Lu<PERSON>jan<PERSON> synagris'},\n", " 34: {'num': '129', 'label': 'Lu<PERSON>janus campechanus'},\n", " 35: {'num': '24', 'label': 'Istiophorus albicans'},\n", " 36: {'num': '388', 'label': 'Cynoscion nebulosus'},\n", " 37: {'num': '217', 'label': 'Elops saurus'},\n", " 38: {'num': '12', 'label': 'Carcharias taurus'},\n", " 39: {'num': '394', 'label': 'Lutjanus griseus'},\n", " 40: {'num': '159', 'label': '<PERSON><PERSON><PERSON><PERSON> carpio'},\n", " 41: {'num': '483', 'label': 'Selene vomer'},\n", " 42: {'num': '192', 'label': '<PERSON><PERSON><PERSON><PERSON> striata'},\n", " 43: {'num': '725', 'label': 'Caranx ruber'},\n", " 44: {'num': '221', 'label': 'Epinephelus morio'},\n", " 45: {'num': '245', 'label': '<PERSON>phip<PERSON> ocellaris'},\n", " 46: {'num': '17', 'label': 'Carcharodon carcharias'},\n", " 47: {'num': '449', 'label': 'Chaetodi<PERSON><PERSON> faber'},\n", " 48: {'num': '481', 'label': 'Mycteroperca microlepis'},\n", " 49: {'num': '676', 'label': 'Lagodon rhomboides'},\n", " 50: {'num': '712', 'label': 'Archosargus probatocephalus'},\n", " 51: {'num': '230', 'label': 'Lobotes surinamensis'},\n", " 52: {'num': '276', 'label': '<PERSON><PERSON><PERSON> gladius'},\n", " 53: {'num': '100', 'label': 'Pomoxis nigromaculatus'},\n", " 54: {'num': '115', 'label': 'Sander vitreus'},\n", " 55: {'num': '21', 'label': 'Rhincodon typus'},\n", " 56: {'num': '696', 'label': 'Oncorhynchus kisutch'},\n", " 57: {'num': '142', 'label': 'Katsuwonus pelamis'},\n", " 58: {'num': '251', 'label': '<PERSON><PERSON><PERSON><PERSON> alletteratus'},\n", " 59: {'num': '2', 'label': 'Lut<PERSON>us analis'},\n", " 60: {'num': '51', 'label': '<PERSON><PERSON><PERSON><PERSON> rubrofuscus'}}"]}, {"cell_type": "code", "execution_count": 14, "id": "c7909d6b", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'num': '336', 'label': 'Pomatomus saltatrix'}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["test[0]"]}, {"cell_type": "code", "execution_count": null, "id": "601565cb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "D2GO", "language": "python", "name": "d2go"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.13"}}, "nbformat": 4, "nbformat_minor": 5}