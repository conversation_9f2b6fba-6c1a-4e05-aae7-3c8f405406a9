{"cells": [{"cell_type": "code", "execution_count": null, "id": "aaa5e114", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "id": "e221bbcc", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.insert(0, '/home/<USER>/<PERSON>/MMLAB/saved_models')\n", "\n", "import os\n", "import time\n", "import json\n", "import logging\n", "\n", "import fiftyone as fo\n", "\n", "from PIL import Image\n", "from torchvision import transforms\n", "\n", "import torchvision\n", "import torch\n", "import numpy as np\n", "import cv2\n", "import matplotlib.pyplot as plt\n", "from tqdm import tqdm\n", "\n", "from export_model.inference import Inference\n", "from model.model_light import FishSeg\n", "from model.dataset import SimpleFishialFishDataset\n", "from model.utils import generate_random_image, draw_polygon, create_mask, visualize, scale_polygon, load_image, max_iou\n", "from shapely.geometry import Polygon\n", "logger = logging.getLogger()\n", "logger.disabled = True"]}, {"cell_type": "code", "execution_count": null, "id": "a723a67b", "metadata": {}, "outputs": [], "source": ["from detector.inference import YOLOInference\n", "from segmentator.inference import Inference\n", "from segmentator.inference import poly_array_to_dict, convert_local_polygons_to_global\n", "from classification_rect.inference import EmbeddingClassifier\n", "from classification_old.inference import EmbeddingClassifier as EmbeddingClassifierOld\n", "\n", "from segmentation.inference_segm import SegmentationInference"]}, {"cell_type": "code", "execution_count": null, "id": "8f866d67", "metadata": {}, "outputs": [], "source": ["def save_json(data, path):\n", "    with open(path, 'w', encoding='utf-8') as f:\n", "        json.dump(data, f)"]}, {"cell_type": "code", "execution_count": null, "id": "4a941a87", "metadata": {}, "outputs": [], "source": ["CLASSIFICATION_RECT_URL = 'https://storage.googleapis.com/fishial-ml-resources/classification_rectangle.zip'\n", "DETECTRON_URL = 'https://storage.googleapis.com/fishial-ml-resources/segmentation.zip'"]}, {"cell_type": "code", "execution_count": null, "id": "2da26927", "metadata": {}, "outputs": [], "source": ["SEGMENTATION_MODEL_PATH = 'saved_models/segmentator/model.ts'\n", "segmentator = Inference(model_path = SEGMENTATION_MODEL_PATH, image_size = 416)"]}, {"cell_type": "code", "execution_count": null, "id": "6ce120e2", "metadata": {}, "outputs": [], "source": ["DETECTRON2_MODEL_PATH = 'saved_models/segmentation/model.ts'\n", "detectron = SegmentationInference(model_path = DETECTRON2_MODEL_PATH)"]}, {"cell_type": "code", "execution_count": null, "id": "2429eca5", "metadata": {}, "outputs": [], "source": ["classification_path = 'saved_models/classification_rect/model.ts'\n", "data_base_path      = 'saved_models/classification_rect/database.pt'\n", "\n", "model_classifier = EmbeddingClassifier(\n", "        classification_path, \n", "        data_base_path\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "98fdff2d", "metadata": {}, "outputs": [], "source": ["classification_path = 'saved_models/classification_old/model.ts'\n", "data_base_path      = 'saved_models/classification_old/embeddings.pt'\n", "data_id_path      = 'saved_models/classification_old/idx.json'\n", "\n", "\n", "model_classifier_v7 = EmbeddingClassifierOld(\n", "        classification_path, \n", "        data_base_path,\n", "        data_id_path = data_id_path)\n"]}, {"cell_type": "code", "execution_count": null, "id": "c5e681ee", "metadata": {"scrolled": true}, "outputs": [], "source": ["# YOLO_MODEL_PATH = 'saved_models/detector/best.torchscript'\n", "YOLO_MODEL_PATH = '/home/<USER>/<PERSON>/yolov9/UltraSegmTrainFISHIAL_OBJECT_DETECTION/yolov10s_640_False/weights/best.torchscript'#'saved_models/detector/model.ts'\n", "\n", "detector = YOLOInference(YOLO_MODEL_PATH, imsz = (640,640), conf_threshold = 0.3, nms_threshold = 0.9, yolo_ver = 'v10')"]}, {"cell_type": "code", "execution_count": null, "id": "05a39ee0", "metadata": {"scrolled": false}, "outputs": [], "source": ["USE_YOLO_SEGMENTATOR = True\n", "DETECTRON2 = True\n", "CLASSIFICATION = True\n", "\n", "line_width = 7\n", "data = fo.load_dataset('SEGM-2024-V0.8')\n", "# data = fo.load_dataset('SEGM-2024-V0.8-VALIDATION')\n", "\n", "count_to_visualize = 20\n", "view = data.take(count_to_visualize)\n", "final_dict = {}\n", "\n", "for sample_id, sample in tqdm(enumerate(view)):\n", "    if sample.image_id in final_dict: continue\n", "        \n", "    \n", "    filepath = sample.filepath\n", "    \n", "    np_img = cv2.imread(filepath)\n", "#     URL = 'https://storage.googleapis.com/backend-fishes-2023/h6raxscor6zx2skzlbz63q5385wo?GoogleAccessId=backend-fishes-storage%40fishial-staging.iam.gserviceaccount.com&Expires=**********&Signature=gbc%2ByuqDbQLqGIfdxRoRurKrywck%2FohEJmReoWMwmFNUMlkDti3Za4kOt2Ppmx1eyWUPpsHju7VylGBt2keH9RM4BYYkib8d9xwca8aAzI0KRLErnaDFVMLOEGnmZkoBw87gQhvGFrOyozudFXWhkc4dMOfVFYSx8d6JeY7ASA8BEgz2o3j2wZNFkGWgJP3AViGUUa6VgOpU9cSmEIBJxytLt%2BUlTZgMyx5uWju7sMR1wGKsBbaP8tH%2F9CpPYzfyY7C85bdwQJ683%2BAWBcbPIBycuEf4XQ75%2FXjG2VrqGAQJUGYuL8%2FvCsMOlzuNGe8UIktMNCHRPdPzCRt1GcQ5Gw%3D%3D&response-content-disposition=inline%3B+filename%3D%22Redband+Parrotfish+-+Sparisoma+aurofrenatum+-+152%252C+Stoplight+Parrotfish+-+Sparisoma+viride+-+TWA+-+155%252C+Striped+Parrotfish+-+Scarus+iseri+-+TWA+-+156P9251257-ChristySemmens.jpg%22%3B+filename%2A%3DUTF-8%27%27Redband%2520Parrotfish%2520-%2520Sparisoma%2520aurofrenatum%2520-%2520152%252C%2520Stoplight%2520Parrotfish%2520-%2520Sparisoma%2520viride%2520-%2520TWA%2520-%2520155%252C%2520Striped%2520Parrotfish%2520-%2520Scarus%2520iseri%2520-%2520TWA%2520-%2520156P9251257-ChristySemmens.jpg&response-content-type=image%2Fjpeg'\n", "#     np_img = load_image({'imageURL': URL})\n", "\n", "    \n", "    img_rgb = cv2.cvtColor(np_img, cv2.COLOR_BGR2RGB)\n", "    pil_image = Image.fromarray(img_rgb)\n", "    \n", "    start_time = time.time()\n", "    \n", "    \n", "    if USE_YOLO_SEGMENTATOR:\n", "        \n", "        boxes = detector.predict(np_img)[0]\n", "        cropped_images = [box_inst.get_mask_BGR() for box_inst in boxes]\n", "    \n", "        np_polygon_yolo = segmentator.predict(cropped_images)\n", "\n", "\n", "#         print(f\"[YOLO] Fish {len(list_of_imgs)} time: {time.time() - start_time}\")\n", "        \n", "        yolo_img = np.array(pil_image)\n", "        if len(cropped_images) != 0:\n", "            \n", "            for poly_id, poly in enumerate(np_polygon_yolo):\n", "                \n", "                poly_inst = np_polygon_yolo[poly_id]\n", "                poly_inst.move_to(boxes[poly_id].x1, boxes[poly_id].y1)\n", "                poly_inst.draw_polygon(yolo_img, color = (255,0,0), thickness = line_width)\n", "        print(f\"DICTIONARY: {[len(output) for output in dict_output]}\")\n", "\n", "    start_time = time.time()        \n", "    if DETECTRON2:\n", "        detectron_img = pil_image.copy() \n", "        \n", "        list_of_boxes_mask_rcnn, mask_rcnn_output = detectron.inference(np_img)\n", "#         mask_rcnn_output = []\n", "        list_of_imgs_detectron2 = []\n", "        shaply_mask_rcnn_poly_detected = []\n", "        for ploy_dict in list_of_boxes_mask_rcnn:\n", "            converted_poly = [(ploy_dict[f\"x{point_id}\"], ploy_dict[f\"y{point_id}\"]) for point_id in range(1, int(len(ploy_dict)/2) + 1)]\n", "            converted_poly_np = np.array(converted_poly)\n", "            x_min, x_max, y_min, y_max = min(converted_poly_np[:, :1])[0], max(converted_poly_np[:, :1])[0], min(converted_poly_np[:, 1:2])[0], max(converted_poly_np[:, 1:2])[0]\n", "            list_of_imgs_detectron2.append(np_img[y_min:y_max, x_min:x_max])\n", "            \n", "            draw_polygon(detectron_img, converted_poly, line_color = (0,255,0), line_width = line_width)\n", "        \n", "\n", "    if len(list_of_imgs_yolo) != 0:\n", "        output_class_yolo = model_classifier.batch_inference(list_of_imgs_yolo)\n", "        \n", "    if len(list_of_imgs_detectron2) != 0:\n", "        output_class_detectron2 = model_classifier_v7.batch_inference(mask_rcnn_output)\n", "\n", "    \n", "    height, width = np_img.shape[:2]\n", "    \n", "    for polylin_inst in sample['General body shape'].polylines:\n", "        polygon = polylin_inst.points[0]\n", "        polygon = scale_polygon(polygon, width, height)\n", "\n", "        draw_polygon(pil_image, polygon, line_color = (0,255,0), line_width = line_width)\n", "\n", "    visualize(\n", "        ground_truth=pil_image, \n", "        DETECTRON2=detectron_img, \n", "        NEW_YOLO=yolo_img\n", "    )\n", "# save_json(final_dict, \"result_actual.json\")"]}, {"cell_type": "code", "execution_count": null, "id": "a83d6ad1", "metadata": {}, "outputs": [], "source": ["URL = 'https://reefguide.org/pix/emperorangelfish2.jpg'\n", "im = load_image({'imageURL': URL})"]}, {"cell_type": "code", "execution_count": null, "id": "5eb921af", "metadata": {}, "outputs": [], "source": ["Image.fromarray(im)"]}, {"cell_type": "code", "execution_count": null, "id": "edb5fdfb", "metadata": {}, "outputs": [], "source": ["!wget https://storage.googleapis.com/fishial-ml-resources/classification.zip"]}, {"cell_type": "code", "execution_count": null, "id": "86545ec1", "metadata": {}, "outputs": [], "source": ["!unzip classification.zip -d classification"]}, {"cell_type": "code", "execution_count": null, "id": "fc522afc", "metadata": {}, "outputs": [], "source": ["list(converted_poly)"]}, {"cell_type": "code", "execution_count": null, "id": "9a4f8c89", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "NEMO", "language": "python", "name": "nemo"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 5}