{"cells": [{"cell_type": "code", "execution_count": 6, "id": "a5001a10", "metadata": {}, "outputs": [], "source": ["import json\n", "import numpy as np\n", "from tqdm import tqdm\n", "import pandas as pd\n", "\n", "from dataclasses import dataclass, asdict\n", "\n", "import os\n", "import requests\n", "import uuid\n", "from urllib.parse import urlparse\n", "from PIL import Image\n", "\n", "def download_image(url_or_path, save_dir=\"save_dir\"):\n", "    \"\"\"\n", "    Checks if the given link is a local image path or a URL.\n", "    If it's a URL, it downloads the image and saves it with a unique name.\n", "\n", "    Args:\n", "        url_or_path (str): The file path or URL of the image.\n", "        save_dir (str): The directory to save the images. Defaults to \"save_dir\".\n", "\n", "    Returns:\n", "        str or None: The path to the saved file if the image was downloaded,\n", "                     or the original path if it was a local file,\n", "                     or None in case of an error.\n", "    \"\"\"\n", "    if not os.path.exists(save_dir):\n", "        os.makedirs(save_dir)\n", "        print(f\"Folder '{save_dir}' created.\")\n", "\n", "    # Check if it's a URL\n", "    parsed_url = urlparse(url_or_path)\n", "    if parsed_url.scheme in ('http', 'https', 'ftp', 'ftps'):\n", "        try:\n", "            response = requests.get(url_or_path, stream=True)\n", "            response.raise_for_status() # Raises an HTTPError for bad responses (4xx or 5xx)\n", "\n", "            # Generate a unique file name\n", "            file_extension = os.path.splitext(parsed_url.path)[1]\n", "            if not file_extension:\n", "                # Attempt to determine extension from Content-Type if not in URL\n", "                content_type = response.headers.get('Content-Type')\n", "                if 'jpeg' in content_type:\n", "                    file_extension = '.jpg'\n", "                elif 'png' in content_type:\n", "                    file_extension = '.png'\n", "                elif 'gif' in content_type:\n", "                    file_extension = '.gif'\n", "                else:\n", "                    file_extension = '.bin' # Fallback\n", "\n", "            unique_filename = str(uuid.uuid4()) + file_extension\n", "            save_path = os.path.join(save_dir, unique_filename)\n", "\n", "            with open(save_path, 'wb') as f:\n", "                for chunk in response.iter_content(chunk_size=8192):\n", "                    f.write(chunk)\n", "            print(f\"Image successfully downloaded and saved: {save_path}\")\n", "            return save_path\n", "        except requests.exceptions.RequestException as e:\n", "            print(f\"Error downloading image from URL '{url_or_path}': {e}\")\n", "            return None\n", "    elif os.path.exists(url_or_path):\n", "        print(f\"Link '{url_or_path}' is a local file.\")\n", "        return url_or_path\n", "    else:\n", "        print(f\"Link '{url_or_path}' is neither a valid URL nor a local file.\")\n", "        return None\n", "    \n", "def highlight_predictions(row):\n", "    \"\"\"\n", "    Applies CSS styles to a row of a DataFrame to highlight model predictions.\n", "    \n", "    This function is intended for use with `DataFrame.style.apply(axis=1)`.\n", "\n", "    - Green: Correct prediction (cell value matches the 'label' column).\n", "    - Salmon: Incorrect prediction.\n", "    - Light Gray: No prediction was made (cell value is None).\n", "    - No Style: Applied to non-model columns (e.g., 'image_id').\n", "    \n", "    Args:\n", "        row (pd.Series): A single row of the DataFrame.\n", "        \n", "    Returns:\n", "        list: A list of CSS style strings, one for each cell in the row.\n", "    \"\"\"\n", "    # Get the ground truth label for the current row\n", "    label = row['label']\n", "    \n", "    # Initialize a list to hold the style for each cell in the row\n", "    styles = []\n", "    \n", "    # Iterate over the index (column names) of the row\n", "    for col_name in row.index:\n", "        # Check if the column is a model prediction column\n", "        if col_name.startswith('model_'):\n", "            value = row[col_name]\n", "            if value is None:\n", "                styles.append('background-color: lightgray')\n", "            elif value == label:\n", "                styles.append('background-color: lightgreen')\n", "            else:\n", "                styles.append('background-color: salmon')\n", "        else:\n", "            # No style for non-model columns ('image_id', 'annotation_id', 'label')\n", "            styles.append('')\n", "            \n", "    return styles\n", "\n", "def run_inference(crop_rgb_np: np.n<PERSON><PERSON>, models: dict, max_k: int) -> dict:\n", "    \"\"\"\n", "    Runs inference on all models for a single crop and formats the result.\n", "\n", "    Args:\n", "        crop_rgb_np: The image crop as a NumPy array.\n", "        models: A dictionary of models to run inference with.\n", "        max_k: The maximum number of predictions to save.\n", "\n", "    Returns:\n", "        A dictionary containing predictions from all models.\n", "    \"\"\"\n", "    model_predictions = {}\n", "    for model_name, model_obj in models.items():\n", "        # Get predictions from the model\n", "        if model_name == 'model_v92':\n", "            input_np_img = crop_rgb_np[:, :, ::-1] # model versiob 92 work on bgr image\n", "        else:\n", "            input_np_img = crop_rgb_np\n", "            \n", "        prediction_group = model_obj.inference_numpy(input_np_img)\n", "        output_labels = [asdict(pred)['name'] for pred in prediction_group]\n", "\n", "        # Save the top-K predictions\n", "        for k in range(max_k):\n", "            key = f\"{model_name}_@{k+1}\"\n", "            try:\n", "                model_predictions[key] = output_labels[k]\n", "            except IndexError:\n", "                # If there are fewer predictions than k, fill with None\n", "                model_predictions[key] = None\n", "\n", "    return model_predictions\n", "\n", "\n", "def process_and_save_results(images_dict: dict, models: dict, max_k: int, output_path: str):\n", "    \"\"\"\n", "    Main function to process images, run inference, and save the results.\n", "    \"\"\"\n", "    all_results = []\n", "\n", "    print(f\"Processing images with {len(models)} models...\")\n", "\n", "    for image_id, image_data in images_dict.items():\n", "        image_path = download_image(image_data['image_url'])\n", "        try:\n", "            img_pil_rgb = Image.open(image_path).convert('RGB')\n", "        except Exception as e:\n", "            print(f\"Could not open image {image_id} from {image_path}. Error: {e}\")\n", "            continue\n", "\n", "        for annotation in image_data['annotations']:\n", "            # Prepare the data for a single row in the final file\n", "            record = {\n", "                'image_id': image_id,\n", "                'annotation_id': annotation['id'],\n", "                'label': annotation['label']\n", "            }\n", "\n", "            # Crop the image based on the annotation\n", "            x, y, w, h = annotation['bbox']\n", "            xyxy = (x, y, x + w, y + h)\n", "            crop_rgb_pil = img_pil_rgb.crop(xyxy)\n", "            crop_rgb_np = np.array(crop_rgb_pil)\n", "            \n", "            # Get predictions from all models\n", "            predictions = run_inference(crop_rgb_np, models, max_k)\n", "            \n", "            # Add the predictions to the record\n", "            record.update(predictions)\n", "\n", "            # Append the complete row to the main list\n", "            all_results.append(record)\n", "    \n", "    if not all_results:\n", "        print(\"No results to save.\")\n", "        return\n", "\n", "    print(\"Creating DataFrame and saving to Excel...\")\n", "    \n", "    # Creating a DataFrame from a list of dictionaries is efficient\n", "    df = pd.DataFrame(all_results)\n", "    \n", "    # Apply styling and save\n", "    styled_df = df.style.apply(highlight_predictions, axis=1)\n", "    styled_df.to_excel(output_path, engine=\"openpyxl\", index=False)\n", "    \n", "    print(f\"Successfully saved results to {output_path}\")\n", "    "]}, {"cell_type": "code", "execution_count": 2, "id": "7a940386", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 12:38:49] [INFO] - Dataset loaded from models/class_v93/database.pt\n", "[2025-06-24 12:38:50] [INFO] - Initializing EmbeddingClassifier...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading ViT backbone: beitv2_base_patch16_224.in1k_ft_in22k_in1k\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 12:38:51] [INFO] - Torch model loaded from models/class_v93/model.ckpt\n", "[2025-06-24 12:38:51] [INFO] - EmbeddingClassifier initialized successfully.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["StableEmbeddingModel initialized with ViT backbone: beitv2_base_patch16_224.in1k_ft_in22k_in1k\n", "  Embedding Dim: 512, Num Classes: 639\n", "  ArcFace s: 64.0, m: 0.5\n", "  Backbone out features (ViT embed_dim): 768\n", "  BN in embedding: False, Dropout in embedding: 0.11\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 12:38:51] [INFO] - Dataset loaded from models/class_v92/database.pt\n", "[2025-06-24 12:38:51] [INFO] - Initializing EmbeddingClassifier...\n", "/home/<USER>/miniconda3/envs/nemo_env/lib/python3.8/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.\n", "  warnings.warn(\n", "/home/<USER>/miniconda3/envs/nemo_env/lib/python3.8/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ConvNeXt_Tiny_Weights.IMAGENET1K_V1`. You can also use `weights=ConvNeXt_Tiny_Weights.DEFAULT` to get the most up-to-date weights.\n", "  warnings.warn(msg)\n", "[2025-06-24 12:38:52] [INFO] - Torch model loaded from models/class_v92/model.ckpt\n", "[2025-06-24 12:38:52] [INFO] - EmbeddingClassifier initialized successfully.\n"]}], "source": ["from models.class_v93.inference import EmbeddingClassifier as EmbeddingClassifierV93\n", "\n", "classifier_configv93 = {\n", "        'model':{\n", "            'path': 'models/class_v93/model.ckpt',\n", "            'device': 'cpu'\n", "        },\n", "        'dataset':{\n", "            'path': 'models/class_v93/database.pt'\n", "        },\n", "        'log_level': 'INFO' \n", "    }\n", "    \n", "model_classifier_v93 = EmbeddingClassifierV93(classifier_configv93)\n", "\n", "from models.class_v92.inference import EmbeddingClassifier as EmbeddingClassifierV92\n", "\n", "classifier_configv92 = {\n", "        'model':{\n", "            'path': 'models/class_v92/model.ckpt',\n", "            'device': 'cpu'\n", "        },\n", "        'dataset':{\n", "            'path': 'models/class_v92/database.pt'\n", "        },\n", "        'log_level': 'INFO'\n", "    }\n", "    \n", "model_classifier_v92 = EmbeddingClassifierV92(classifier_configv92)"]}, {"cell_type": "code", "execution_count": 3, "id": "5f2324b3", "metadata": {}, "outputs": [], "source": ["# ================== CONFIG ==================\n", "# 1. Set the maximum number of top predictions to save\n", "MAX_TOP_K = 3\n", "\n", "# 2. Define all the models you want to use.\n", "#    The key is the name that will be used in the column headers.\n", "#    The value is your model object, which must have an `inference_numpy` method.\n", "MODELS_TO_RUN = {\n", "    \"model_v92\": model_classifier_v92,\n", "    \"model_v93\": model_classifier_v93,\n", "    # Add other models here following the same pattern\n", "    # \"another_model\": load_another_model(),\n", "}\n", "\n", "# 3. Name of the output Excel file\n", "OUTPUT_FILENAME = \"v9_test_dataset.xlsx\"\n", "# ============================================"]}, {"cell_type": "code", "execution_count": 9, "id": "ef312230", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processed 11847 images.\n", "Found 12045 relevant annotations.\n"]}], "source": ["import json\n", "import numpy as np\n", "from collections import defaultdict\n", "\n", "# Define the path to the COCO JSON file\n", "coco_file_path = '/home/<USER>/Fishial/dataset/EXPORT_V_0_9/Fishial_Export_Mar_25_2025_02_47_Prod_Export_Test_Images_for_testing.json'\n", "\n", "# Load the COCO data from the file\n", "try:\n", "    with open(coco_file_path, 'r') as f:\n", "        data = json.load(f)\n", "except FileNotFoundError:\n", "    print(f\"Error: The file '{coco_file_path}' was not found.\")\n", "    exit()\n", "except json.JSONDecodeError:\n", "    print(f\"Error: The file '{coco_file_path}' is not a valid JSON file.\")\n", "    exit()\n", "\n", "id_to_label = {\n", "    category['id']: category['supercategory']\n", "    for category in data.get('categories', [])\n", "    if category.get('name') == 'General body shape'\n", "}\n", "\n", "# Initialize the images dictionary using a dictionary comprehension.\n", "# Using defaultdict to simplify appending annotations.\n", "images_dict = {\n", "    image['id']: {\n", "        'image_url': image['coco_url'],\n", "        'annotations': []\n", "    }\n", "    for image in data.get('images', [])\n", "}\n", "\n", "# Process annotations and add them to the corresponding images\n", "for annotation in data.get('annotations', []):\n", "    category_id = annotation.get('category_id')\n", "    image_id = annotation.get('image_id')\n", "\n", "    # Check if the annotation is valid and relevant\n", "    if 'bbox' in annotation and image_id in images_dict and category_id in id_to_label:\n", "        images_dict[image_id]['annotations'].append({\n", "            'bbox': np.array(annotation['bbox']).astype(np.int32),\n", "            'label': id_to_label[category_id],\n", "            'id': annotation['id']\n", "        })\n", "\n", "# The 'images_dict' is now populated with the desired data.\n", "# You can now, for example, print the number of images and annotations found.\n", "print(f\"Processed {len(images_dict)} images.\")\n", "total_annotations = sum(len(img['annotations']) for img in images_dict.values())\n", "print(f\"Found {total_annotations} relevant annotations.\")"]}, {"cell_type": "code", "execution_count": null, "id": "0881b1c8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing images with 2 models...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:19] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:19] [INFO] - Completed in 0.00 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/2e8e45d3-d9ef-4bcf-8dbf-8976c7e8f518.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:19] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:19] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:19] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:19] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:20] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:20] [INFO] - Completed in 0.03 seconds\n", "[2025-06-24 15:00:20] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:20] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:20] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:20] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:20] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:20] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:20] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:20] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:22] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:22] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:22] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/808b0c85-9111-4ffc-85da-cc7f08cb786e.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:22] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:22] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:22] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:23] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:23] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:23] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:23] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:23] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:23] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:23] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:23] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:23] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:23] [INFO] - Completed in 0.03 seconds\n", "[2025-06-24 15:00:23] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:23] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:23] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:23] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:24] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:24] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:24] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:24] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:24] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:24] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:24] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:24] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:24] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:24] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:24] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:24] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:24] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:24] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:25] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:25] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:25] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:25] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:25] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:25] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:25] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:25] [INFO] - Completed in 0.00 seconds\n", "[2025-06-24 15:00:25] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:25] [INFO] - Completed in 0.00 seconds\n", "[2025-06-24 15:00:25] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:25] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:25] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:25] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:25] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:25] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:26] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:26] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:26] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:26] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:26] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:26] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:26] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:26] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:26] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:26] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:26] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:26] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:26] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:27] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:27] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:27] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:27] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:27] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:27] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:27] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:27] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:27] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:27] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:27] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:27] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:27] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:27] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:27] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:28] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:28] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:28] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:28] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:28] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:28] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:28] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:28] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:28] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:28] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:29] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:29] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:29] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/e8f8a5ce-8a19-41dd-b5bf-6292fb8f2359.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:29] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:29] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:29] [INFO] - Completed in 0.01 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/81f82287-8de4-4d3a-86c8-67fcd615d51c.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:30] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:30] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:30] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:30] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:30] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/820842d6-f2ba-4338-b3e7-9c522a62696b.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:30] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:30] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:30] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:30] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:30] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:31] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:31] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:31] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:31] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:31] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:31] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:31] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:31] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:31] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:31] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:31] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:31] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:31] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:31] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:32] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:32] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:32] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:32] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:32] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:32] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:32] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:32] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:32] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:32] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:32] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:32] [INFO] - Completed in 0.03 seconds\n", "[2025-06-24 15:00:32] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:32] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:33] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:33] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:33] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:33] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:33] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:33] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:33] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:33] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:33] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:33] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:34] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/28080519-52d7-4cbb-a390-e46990b7fffb.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:34] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:34] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:34] [INFO] - Completed in 0.00 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/2da74d52-dd53-4ebb-b429-a24f3dcef7ed.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:34] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:34] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:34] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:34] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:35] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:35] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:35] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:35] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:35] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:35] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:35] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:35] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:35] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:35] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:35] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:35] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:35] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:35] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:35] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:36] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:36] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:36] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:36] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:36] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:36] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:36] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:36] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:36] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:37] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/ee01d6a7-11ec-45e6-a8ed-e9ded410b66f.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:37] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:37] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:37] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:37] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/fa970580-ab45-443b-a165-12fc2295967a.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:37] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:37] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:37] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:37] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:37] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:38] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:38] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:38] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:38] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:38] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:38] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:38] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:38] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:38] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:38] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:38] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:38] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:38] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:38] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:39] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:39] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:39] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:39] [INFO] - Completed in 0.01 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/cc5f55be-3265-4172-88ca-9262228c1e16.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:39] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:39] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:40] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:40] [INFO] - Completed in 0.02 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/440c9a43-acca-4c8c-afed-9f17d97a8015.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:40] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:40] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:40] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:40] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:40] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:40] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:40] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:40] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:40] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:40] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:40] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:40] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:41] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:41] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:41] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:41] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:41] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:41] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:41] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:41] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:41] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:41] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:41] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:41] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:41] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:41] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:42] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:42] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:42] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:42] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:42] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:42] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:42] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:42] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:42] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:42] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:42] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:42] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:43] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:43] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:43] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/84b26a66-0769-4ddf-92fc-834443611ee8.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:43] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:43] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:43] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:43] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/9147f4c3-f556-4e8e-b607-1c533bbac267.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:43] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:44] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:44] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:44] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:44] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:44] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:44] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:44] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:44] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:44] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:44] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:44] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:44] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:45] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:45] [INFO] - Completed in 0.01 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/6e257444-42b6-49c5-98c6-c868d7523a25.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:45] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:45] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:45] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:45] [INFO] - Completed in 0.01 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/554dd574-55ab-43c6-8710-aafc4780dcc9.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:45] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:45] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:46] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:46] [INFO] - Completed in 0.02 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/79bd2c4b-0652-4aca-95c2-8b10cfdfbb7c.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:46] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:46] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:49] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:49] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:49] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/31df12c1-14cd-45ac-9657-069df2351d38.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:49] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:49] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:49] [INFO] - Completed in 0.00 seconds\n", "[2025-06-24 15:00:49] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:49] [INFO] - Completed in 0.00 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/faf3a78d-9eed-4d9d-8d29-3cbccb2b5a99.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:50] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:50] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:50] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/6b3b5558-2c2c-4364-95ff-015f842e575e.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:50] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:50] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:50] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:50] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/f8654718-aa55-470f-b868-e7a5763f5806.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:50] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:51] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:51] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:51] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/17ff8099-5143-45d6-96e3-c30add4d76e3.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:51] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:53] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:53] [INFO] - Completed in 0.03 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/d3f005f4-7d45-437a-9051-711b96fc34ac.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:53] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:53] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:53] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:53] [INFO] - Completed in 0.02 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/e41c4c70-1273-403b-b1df-3890a754a13c.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:53] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:53] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:54] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:54] [INFO] - Completed in 0.01 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/9d847321-10b2-4686-8439-60e618e89b13.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:54] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:54] [INFO] - Completed in 0.00 seconds\n", "[2025-06-24 15:00:54] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:54] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:54] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:54] [INFO] - Completed in 0.01 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/c34added-947c-44d2-a45f-101c8dd676a6.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:55] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:55] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:55] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/14b8f048-00b8-4c14-8f7f-d9bf238bc1ef.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:55] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:55] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:55] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:55] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/591931ac-a247-4969-afb4-73efb290dadc.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:55] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:56] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:56] [INFO] - Completed in 0.01 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/87a0fa14-fb5e-4102-b700-200212cc81b7.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:56] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:56] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:56] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:56] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:57] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/9fcc1d3a-9fa9-4d7b-a5c0-e2b09c44926a.png\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:57] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:57] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:57] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:57] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/c11aed6c-7309-4bb8-88fe-ebd05dbb204f.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:57] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:57] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:57] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:57] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:57] [INFO] - Completed in 0.03 seconds\n", "[2025-06-24 15:00:58] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:58] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:58] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:58] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:58] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:58] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:58] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:58] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:58] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:58] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:58] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:58] [INFO] - Completed in 0.00 seconds\n", "[2025-06-24 15:00:58] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:58] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:59] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:59] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:59] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:59] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:00:59] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:59] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:00:59] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:59] [INFO] - Completed in 0.01 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/d295b7ba-37e4-4cea-beec-2301a42179ef.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:00:59] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:00:59] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:00] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:00] [INFO] - Completed in 0.00 seconds\n", "[2025-06-24 15:01:00] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/8ab06bbe-41a4-4d5a-8fd6-e833e95aa3a6.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:00] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:01] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:01] [INFO] - Completed in 0.03 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/f036a0cd-0386-4b6d-9680-ab3088d4986b.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:01] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:01] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:01] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:01] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:01] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:01] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:02] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:02] [INFO] - Completed in 0.03 seconds\n", "[2025-06-24 15:01:02] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/a3644a0e-95b6-44cf-9efc-fa7f409ccf5b.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:02] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:02] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:02] [INFO] - Completed in 0.03 seconds\n", "[2025-06-24 15:01:02] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:02] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:02] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:02] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:02] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:02] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:02] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:02] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:03] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:03] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:03] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:03] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:03] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:03] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:03] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:03] [INFO] - Completed in 0.00 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/1c255eba-8d25-4a0b-94a1-28a6cd9fc196.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:03] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:03] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:04] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:04] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:04] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:04] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:04] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:04] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:04] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:04] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:04] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:04] [INFO] - Completed in 0.01 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/aee25146-a4f6-41c5-bea3-bc791c11c76d.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:05] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:05] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:05] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:05] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:05] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:05] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:05] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:05] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:05] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:05] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:05] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:05] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:05] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:05] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:05] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:05] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:06] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:06] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:06] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:06] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:06] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:06] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:06] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:06] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:06] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:06] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:07] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:07] [INFO] - Completed in 0.00 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/f4049627-5270-4750-8d54-8cb866e69cab.jpeg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:07] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:07] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:07] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:07] [INFO] - Completed in 0.01 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/d28322a7-7c17-47a4-bef5-2a7e5d44e2f2.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:07] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:07] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:08] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:08] [INFO] - Completed in 0.02 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/38b74968-c187-4e93-b8e7-9ea7e0da5aee.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:08] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:08] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:08] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:08] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:08] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:08] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:10] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:10] [INFO] - Completed in 0.01 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/5c7e111f-77f8-408c-b26f-e0fb965ab8ae.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:10] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:10] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:10] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:10] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:10] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:10] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:11] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:11] [INFO] - Completed in 0.02 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/532a2f94-ae04-4ac2-87cb-507f9371963f.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:11] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:11] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:13] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:13] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:13] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/7afe6321-c11e-452a-9e0c-c098f8ff5c62.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:13] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:14] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:14] [INFO] - Completed in 0.01 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/344d136f-e31a-4730-8858-832787f44be5.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:15] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:15] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:15] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:15] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:15] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/2433911f-b590-4e03-9711-d0253ae30c86.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:15] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:16] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:16] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:16] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/21c48a82-10e7-4f44-8b60-732d306cce95.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:16] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:16] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:16] [INFO] - Completed in 0.00 seconds\n", "[2025-06-24 15:01:16] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/bcccf356-0b95-44fd-891b-f5721bbed91e.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:16] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:17] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:17] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:17] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/6cc3e2ed-654c-49cc-9970-73df0fe7764c.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:17] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:17] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:17] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:17] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/cf16831a-c8f8-4a5c-939d-af82ab3b05ac.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:17] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:18] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:18] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:18] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/4bc42827-08f5-4808-a9d6-c808c4eeb371.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:18] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:18] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:18] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:18] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/764bffaf-80a8-4d44-a8a4-7f51c7cbecb9.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:18] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:19] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:19] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:19] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/dccd6c8e-39c0-43e9-b674-bd2603b7445c.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:19] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:19] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:19] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:20] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/ad259ce5-f8aa-4699-8f66-299b46f0870d.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:20] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:20] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:20] [INFO] - Completed in 0.03 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/d7c65dd2-62ff-4690-8bf1-a9e1c7e6bca3.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:20] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:20] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:21] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:21] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:21] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:21] [INFO] - Completed in 0.01 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/0f3a4577-36c7-408b-9f8a-d9862f04cdca.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:21] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:21] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:21] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/98de9032-16f6-4637-845b-72e3348f3ee5.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:21] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:22] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:22] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:22] [INFO] - Starting search over 1 embeddings\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/b3000124-9f43-4846-93ac-113eac46c4e3.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:22] [INFO] - Completed in 0.01 seconds\n", "[2025-06-24 15:01:22] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:22] [INFO] - Completed in 0.03 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/3f7ac1a2-cb20-4e3e-91b7-d6d4923dc09b.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:22] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:22] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:23] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:23] [INFO] - Completed in 0.01 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Image successfully downloaded and saved: save_dir/c37e707a-692e-4137-aa1b-ba3eceb6f103.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-06-24 15:01:23] [INFO] - Starting search over 1 embeddings\n", "[2025-06-24 15:01:23] [INFO] - Completed in 0.02 seconds\n", "[2025-06-24 15:01:23] [INFO] - Starting search over 1 embeddings\n"]}], "source": ["# Start the main process\n", "process_and_save_results(\n", "    images_dict=images_dict,\n", "    models=MODELS_TO_RUN,\n", "    max_k=MAX_TOP_K,\n", "    output_path=OUTPUT_FILENAME\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "af8c211a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "NEMO", "language": "python", "name": "nemo"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 5}